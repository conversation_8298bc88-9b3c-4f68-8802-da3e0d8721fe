[options]
admin_passwd = $pbkdf2-sha512$600000$3zvnHOP8P.ccg9CaU0ppTQ$XZQyD8GOFdGuafMHqUg9MTyQF.nwXaIhIjqDcv6vkSv05Cqlh70oAKAUvwvflZkiJ/sx8D1TogsD5m8JLb3hSA
db_host = database18-stack
db_port = 5432
db_user = odoo
db_password = odoo
addons_path = /mnt/extra-addons,/usr/lib/python3/dist-packages/odoo/addons
data_dir = /var/lib/odoo
db_maxconn = 32
db_template = template0
db_sslmode = prefer
limit_memory_soft = 671088640
limit_memory_hard = 805306368
limit_request = 4096
limit_time_cpu = 60
limit_time_real = 120
max_cron_threads = 1
workers = 0
db_filter = .*
log_handler = :INFO
log_level = info
logfile = /var/log/odoo/odoo.log
logrotate = True
proxy_mode = True
server_wide_modules = web
websocket = True
gevent_port = 8072
list_db = True
without_demo = True
osv_memory_count_limit = 1
osv_memory_age_limit = 1

