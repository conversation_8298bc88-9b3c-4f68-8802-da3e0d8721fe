2025-04-04 07:39:40,982 - INFO - Starting embedding helper script
2025-04-04 07:39:40,982 - INFO - Reading input from stdin
2025-04-04 07:39:40,983 - INFO - Input data length: 897
2025-04-04 07:39:40,983 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=851
2025-04-04 07:39:40,983 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:39:40,983 - INFO - Text length: 851
2025-04-04 07:39:42,815 - INFO - Torch threads set to 1
2025-04-04 07:39:46,566 - INFO - Loading model...
2025-04-04 07:39:46,567 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:39:49,548 - INFO - Model loaded successfully
2025-04-04 07:39:49,549 - INFO - Generating embedding...
2025-04-04 07:39:49,889 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:39:49,891 - INFO - Returning successful result
2025-04-04 07:39:49,891 - INFO - Writing result to stdout
2025-04-04 07:39:49,892 - INFO - Result written successfully
2025-04-04 07:39:51,517 - INFO - Starting embedding helper script
2025-04-04 07:39:51,518 - INFO - Reading input from stdin
2025-04-04 07:39:51,518 - INFO - Input data length: 1024
2025-04-04 07:39:51,519 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=978
2025-04-04 07:39:51,519 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:39:51,520 - INFO - Text length: 978
2025-04-04 07:39:53,593 - INFO - Torch threads set to 1
2025-04-04 07:39:57,324 - INFO - Loading model...
2025-04-04 07:39:57,325 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:40:00,171 - INFO - Model loaded successfully
2025-04-04 07:40:00,172 - INFO - Generating embedding...
2025-04-04 07:40:00,294 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:40:00,294 - INFO - Returning successful result
2025-04-04 07:40:00,295 - INFO - Writing result to stdout
2025-04-04 07:40:00,295 - INFO - Result written successfully
2025-04-04 07:40:01,474 - INFO - Starting embedding helper script
2025-04-04 07:40:01,474 - INFO - Reading input from stdin
2025-04-04 07:40:01,475 - INFO - Input data length: 937
2025-04-04 07:40:01,475 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=891
2025-04-04 07:40:01,475 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:40:01,475 - INFO - Text length: 891
2025-04-04 07:40:03,785 - INFO - Torch threads set to 1
2025-04-04 07:40:09,755 - INFO - Loading model...
2025-04-04 07:40:09,757 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:40:12,774 - INFO - Model loaded successfully
2025-04-04 07:40:12,775 - INFO - Generating embedding...
2025-04-04 07:40:13,485 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:40:13,487 - INFO - Returning successful result
2025-04-04 07:40:13,487 - INFO - Writing result to stdout
2025-04-04 07:40:13,487 - INFO - Result written successfully
2025-04-04 07:53:03,498 - INFO - Starting embedding helper script
2025-04-04 07:53:03,499 - INFO - Reading input from stdin
2025-04-04 07:53:03,499 - INFO - Input data length: 897
2025-04-04 07:53:03,499 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=851
2025-04-04 07:53:03,499 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:53:03,500 - INFO - Text length: 851
2025-04-04 07:53:05,166 - INFO - Torch threads set to 1
2025-04-04 07:53:08,010 - INFO - Loading model...
2025-04-04 07:53:08,011 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:53:10,871 - INFO - Model loaded successfully
2025-04-04 07:53:10,871 - INFO - Generating embedding...
2025-04-04 07:53:11,146 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:53:11,147 - INFO - Returning successful result
2025-04-04 07:53:11,147 - INFO - Writing result to stdout
2025-04-04 07:53:11,148 - INFO - Result written successfully
2025-04-04 07:53:11,942 - INFO - Starting embedding helper script
2025-04-04 07:53:11,942 - INFO - Reading input from stdin
2025-04-04 07:53:11,943 - INFO - Input data length: 1024
2025-04-04 07:53:11,943 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=978
2025-04-04 07:53:11,943 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:53:11,943 - INFO - Text length: 978
2025-04-04 07:53:13,371 - INFO - Torch threads set to 1
2025-04-04 07:53:16,152 - INFO - Loading model...
2025-04-04 07:53:16,152 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:53:18,868 - INFO - Model loaded successfully
2025-04-04 07:53:18,869 - INFO - Generating embedding...
2025-04-04 07:53:19,012 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:53:19,013 - INFO - Returning successful result
2025-04-04 07:53:19,013 - INFO - Writing result to stdout
2025-04-04 07:53:19,014 - INFO - Result written successfully
2025-04-04 07:53:19,836 - INFO - Starting embedding helper script
2025-04-04 07:53:19,837 - INFO - Reading input from stdin
2025-04-04 07:53:19,837 - INFO - Input data length: 937
2025-04-04 07:53:19,837 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=891
2025-04-04 07:53:19,837 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:53:19,838 - INFO - Text length: 891
2025-04-04 07:53:21,136 - INFO - Torch threads set to 1
2025-04-04 07:53:23,863 - INFO - Loading model...
2025-04-04 07:53:23,864 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:53:26,516 - INFO - Model loaded successfully
2025-04-04 07:53:26,516 - INFO - Generating embedding...
2025-04-04 07:53:26,602 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:53:26,602 - INFO - Returning successful result
2025-04-04 07:53:26,602 - INFO - Writing result to stdout
2025-04-04 07:53:26,603 - INFO - Result written successfully
2025-04-04 07:53:27,543 - INFO - Starting embedding helper script
2025-04-04 07:53:27,543 - INFO - Reading input from stdin
2025-04-04 07:53:27,543 - INFO - Input data length: 755
2025-04-04 07:53:27,544 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=709
2025-04-04 07:53:27,544 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:53:27,544 - INFO - Text length: 709
2025-04-04 07:53:28,951 - INFO - Torch threads set to 1
2025-04-04 07:53:32,345 - INFO - Loading model...
2025-04-04 07:53:32,346 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:53:35,423 - INFO - Model loaded successfully
2025-04-04 07:53:35,423 - INFO - Generating embedding...
2025-04-04 07:53:35,499 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:53:35,500 - INFO - Returning successful result
2025-04-04 07:53:35,500 - INFO - Writing result to stdout
2025-04-04 07:53:35,501 - INFO - Result written successfully
2025-04-04 07:53:36,431 - INFO - Starting embedding helper script
2025-04-04 07:53:36,431 - INFO - Reading input from stdin
2025-04-04 07:53:36,432 - INFO - Input data length: 1017
2025-04-04 07:53:36,432 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=971
2025-04-04 07:53:36,432 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:53:36,432 - INFO - Text length: 971
2025-04-04 07:53:37,757 - INFO - Torch threads set to 1
2025-04-04 07:53:41,237 - INFO - Loading model...
2025-04-04 07:53:41,238 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:53:43,940 - INFO - Model loaded successfully
2025-04-04 07:53:43,947 - INFO - Generating embedding...
2025-04-04 07:53:44,063 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:53:44,063 - INFO - Returning successful result
2025-04-04 07:53:44,064 - INFO - Writing result to stdout
2025-04-04 07:53:44,064 - INFO - Result written successfully
2025-04-04 07:53:45,006 - INFO - Starting embedding helper script
2025-04-04 07:53:45,006 - INFO - Reading input from stdin
2025-04-04 07:53:45,006 - INFO - Input data length: 897
2025-04-04 07:53:45,007 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=851
2025-04-04 07:53:45,007 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:53:45,007 - INFO - Text length: 851
2025-04-04 07:53:46,263 - INFO - Torch threads set to 1
2025-04-04 07:53:49,066 - INFO - Loading model...
2025-04-04 07:53:49,066 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:53:52,707 - INFO - Model loaded successfully
2025-04-04 07:53:52,707 - INFO - Generating embedding...
2025-04-04 07:53:52,814 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:53:52,815 - INFO - Returning successful result
2025-04-04 07:53:52,815 - INFO - Writing result to stdout
2025-04-04 07:53:52,816 - INFO - Result written successfully
2025-04-04 07:53:53,672 - INFO - Starting embedding helper script
2025-04-04 07:53:53,672 - INFO - Reading input from stdin
2025-04-04 07:53:53,672 - INFO - Input data length: 857
2025-04-04 07:53:53,672 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=811
2025-04-04 07:53:53,672 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:53:53,673 - INFO - Text length: 811
2025-04-04 07:53:55,023 - INFO - Torch threads set to 1
2025-04-04 07:53:57,706 - INFO - Loading model...
2025-04-04 07:53:57,707 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:54:00,748 - INFO - Model loaded successfully
2025-04-04 07:54:00,748 - INFO - Generating embedding...
2025-04-04 07:54:00,839 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:54:00,840 - INFO - Returning successful result
2025-04-04 07:54:00,840 - INFO - Writing result to stdout
2025-04-04 07:54:00,840 - INFO - Result written successfully
2025-04-04 07:54:01,686 - INFO - Starting embedding helper script
2025-04-04 07:54:01,687 - INFO - Reading input from stdin
2025-04-04 07:54:01,687 - INFO - Input data length: 1030
2025-04-04 07:54:01,687 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=984
2025-04-04 07:54:01,687 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:54:01,688 - INFO - Text length: 984
2025-04-04 07:54:03,077 - INFO - Torch threads set to 1
2025-04-04 07:54:05,651 - INFO - Loading model...
2025-04-04 07:54:05,652 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:54:08,400 - INFO - Model loaded successfully
2025-04-04 07:54:08,400 - INFO - Generating embedding...
2025-04-04 07:54:08,499 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:54:08,500 - INFO - Returning successful result
2025-04-04 07:54:08,500 - INFO - Writing result to stdout
2025-04-04 07:54:08,500 - INFO - Result written successfully
2025-04-04 07:54:09,317 - INFO - Starting embedding helper script
2025-04-04 07:54:09,317 - INFO - Reading input from stdin
2025-04-04 07:54:09,318 - INFO - Input data length: 912
2025-04-04 07:54:09,318 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=866
2025-04-04 07:54:09,318 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:54:09,318 - INFO - Text length: 866
2025-04-04 07:54:10,595 - INFO - Torch threads set to 1
2025-04-04 07:54:13,422 - INFO - Loading model...
2025-04-04 07:54:13,423 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:54:16,050 - INFO - Model loaded successfully
2025-04-04 07:54:16,050 - INFO - Generating embedding...
2025-04-04 07:54:16,158 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:54:16,159 - INFO - Returning successful result
2025-04-04 07:54:16,159 - INFO - Writing result to stdout
2025-04-04 07:54:16,160 - INFO - Result written successfully
2025-04-04 07:54:17,023 - INFO - Starting embedding helper script
2025-04-04 07:54:17,024 - INFO - Reading input from stdin
2025-04-04 07:54:17,024 - INFO - Input data length: 994
2025-04-04 07:54:17,024 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=948
2025-04-04 07:54:17,024 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:54:17,025 - INFO - Text length: 948
2025-04-04 07:54:18,455 - INFO - Torch threads set to 1
2025-04-04 07:54:21,320 - INFO - Loading model...
2025-04-04 07:54:21,321 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:54:24,515 - INFO - Model loaded successfully
2025-04-04 07:54:24,515 - INFO - Generating embedding...
2025-04-04 07:54:24,607 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:54:24,607 - INFO - Returning successful result
2025-04-04 07:54:24,608 - INFO - Writing result to stdout
2025-04-04 07:54:24,608 - INFO - Result written successfully
2025-04-04 07:54:25,461 - INFO - Starting embedding helper script
2025-04-04 07:54:25,462 - INFO - Reading input from stdin
2025-04-04 07:54:25,462 - INFO - Input data length: 1025
2025-04-04 07:54:25,462 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=979
2025-04-04 07:54:25,462 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:54:25,462 - INFO - Text length: 979
2025-04-04 07:54:27,130 - INFO - Torch threads set to 1
2025-04-04 07:54:30,141 - INFO - Loading model...
2025-04-04 07:54:30,142 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:54:32,868 - INFO - Model loaded successfully
2025-04-04 07:54:32,868 - INFO - Generating embedding...
2025-04-04 07:54:32,988 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:54:32,988 - INFO - Returning successful result
2025-04-04 07:54:32,989 - INFO - Writing result to stdout
2025-04-04 07:54:32,989 - INFO - Result written successfully
2025-04-04 07:54:33,803 - INFO - Starting embedding helper script
2025-04-04 07:54:33,803 - INFO - Reading input from stdin
2025-04-04 07:54:33,803 - INFO - Input data length: 544
2025-04-04 07:54:33,804 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=498
2025-04-04 07:54:33,804 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:54:33,804 - INFO - Text length: 498
2025-04-04 07:54:35,112 - INFO - Torch threads set to 1
2025-04-04 07:54:37,819 - INFO - Loading model...
2025-04-04 07:54:37,820 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:54:40,455 - INFO - Model loaded successfully
2025-04-04 07:54:40,456 - INFO - Generating embedding...
2025-04-04 07:54:40,507 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:54:40,508 - INFO - Returning successful result
2025-04-04 07:54:40,508 - INFO - Writing result to stdout
2025-04-04 07:54:40,508 - INFO - Result written successfully
2025-04-04 07:54:41,380 - INFO - Starting embedding helper script
2025-04-04 07:54:41,380 - INFO - Reading input from stdin
2025-04-04 07:54:41,380 - INFO - Input data length: 1026
2025-04-04 07:54:41,381 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=980
2025-04-04 07:54:41,381 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:54:41,381 - INFO - Text length: 980
2025-04-04 07:54:43,238 - INFO - Torch threads set to 1
2025-04-04 07:54:51,505 - INFO - Loading model...
2025-04-04 07:54:51,506 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:54:54,210 - INFO - Model loaded successfully
2025-04-04 07:54:54,211 - INFO - Generating embedding...
2025-04-04 07:54:54,334 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:54:54,334 - INFO - Returning successful result
2025-04-04 07:54:54,335 - INFO - Writing result to stdout
2025-04-04 07:54:54,335 - INFO - Result written successfully
2025-04-04 07:54:55,276 - INFO - Starting embedding helper script
2025-04-04 07:54:55,276 - INFO - Reading input from stdin
2025-04-04 07:54:55,277 - INFO - Input data length: 999
2025-04-04 07:54:55,277 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=953
2025-04-04 07:54:55,277 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:54:55,277 - INFO - Text length: 953
2025-04-04 07:54:56,925 - INFO - Torch threads set to 1
2025-04-04 07:55:00,837 - INFO - Loading model...
2025-04-04 07:55:00,838 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:55:03,753 - INFO - Model loaded successfully
2025-04-04 07:55:03,754 - INFO - Generating embedding...
2025-04-04 07:55:03,883 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:55:03,883 - INFO - Returning successful result
2025-04-04 07:55:03,884 - INFO - Writing result to stdout
2025-04-04 07:55:03,884 - INFO - Result written successfully
2025-04-04 07:55:04,686 - INFO - Starting embedding helper script
2025-04-04 07:55:04,687 - INFO - Reading input from stdin
2025-04-04 07:55:04,687 - INFO - Input data length: 999
2025-04-04 07:55:04,687 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=953
2025-04-04 07:55:04,687 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:55:04,688 - INFO - Text length: 953
2025-04-04 07:55:06,040 - INFO - Torch threads set to 1
2025-04-04 07:55:08,856 - INFO - Loading model...
2025-04-04 07:55:08,857 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:55:11,644 - INFO - Model loaded successfully
2025-04-04 07:55:11,644 - INFO - Generating embedding...
2025-04-04 07:55:11,746 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:55:11,746 - INFO - Returning successful result
2025-04-04 07:55:11,747 - INFO - Writing result to stdout
2025-04-04 07:55:11,747 - INFO - Result written successfully
2025-04-04 07:55:12,720 - INFO - Starting embedding helper script
2025-04-04 07:55:12,720 - INFO - Reading input from stdin
2025-04-04 07:55:12,720 - INFO - Input data length: 922
2025-04-04 07:55:12,720 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=876
2025-04-04 07:55:12,721 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-04 07:55:12,721 - INFO - Text length: 876
2025-04-04 07:55:14,094 - INFO - Torch threads set to 1
2025-04-04 07:55:16,964 - INFO - Loading model...
2025-04-04 07:55:16,965 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-04 07:55:19,604 - INFO - Model loaded successfully
2025-04-04 07:55:19,604 - INFO - Generating embedding...
2025-04-04 07:55:19,680 - INFO - Embedding generated successfully with shape: (384,)
2025-04-04 07:55:19,680 - INFO - Returning successful result
2025-04-04 07:55:19,680 - INFO - Writing result to stdout
2025-04-04 07:55:19,680 - INFO - Result written successfully
2025-04-05 08:57:20,697 - INFO - Starting embedding helper script
2025-04-05 08:57:20,698 - INFO - Reading input from stdin
2025-04-05 08:57:20,700 - INFO - Input data length: 1130
2025-04-05 08:57:20,700 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=984
2025-04-05 08:57:20,701 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:57:20,701 - INFO - Text length: 984
2025-04-05 08:57:23,050 - INFO - Torch threads set to 1
2025-04-05 08:57:27,783 - INFO - Loading model...
2025-04-05 08:57:27,785 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:57:34,277 - INFO - Model loaded successfully
2025-04-05 08:57:34,277 - INFO - Generating embedding...
2025-04-05 08:57:34,748 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:57:34,749 - INFO - Returning successful result
2025-04-05 08:57:34,750 - INFO - Writing result to stdout
2025-04-05 08:57:34,750 - INFO - Result written successfully
2025-04-05 08:57:35,693 - INFO - Starting embedding helper script
2025-04-05 08:57:35,693 - INFO - Reading input from stdin
2025-04-05 08:57:35,694 - INFO - Input data length: 976
2025-04-05 08:57:35,694 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=930
2025-04-05 08:57:35,694 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:57:35,695 - INFO - Text length: 930
2025-04-05 08:57:37,998 - INFO - Torch threads set to 1
2025-04-05 08:57:42,093 - INFO - Loading model...
2025-04-05 08:57:42,096 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:57:47,071 - INFO - Model loaded successfully
2025-04-05 08:57:47,072 - INFO - Generating embedding...
2025-04-05 08:57:47,228 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:57:47,229 - INFO - Returning successful result
2025-04-05 08:57:47,229 - INFO - Writing result to stdout
2025-04-05 08:57:47,230 - INFO - Result written successfully
2025-04-05 08:57:48,138 - INFO - Starting embedding helper script
2025-04-05 08:57:48,139 - INFO - Reading input from stdin
2025-04-05 08:57:48,139 - INFO - Input data length: 1006
2025-04-05 08:57:48,139 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=960
2025-04-05 08:57:48,140 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:57:48,140 - INFO - Text length: 960
2025-04-05 08:57:50,350 - INFO - Torch threads set to 1
2025-04-05 08:57:54,664 - INFO - Loading model...
2025-04-05 08:57:54,666 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:57:59,567 - INFO - Model loaded successfully
2025-04-05 08:57:59,567 - INFO - Generating embedding...
2025-04-05 08:57:59,704 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:57:59,705 - INFO - Returning successful result
2025-04-05 08:57:59,705 - INFO - Writing result to stdout
2025-04-05 08:57:59,706 - INFO - Result written successfully
2025-04-05 08:58:00,612 - INFO - Starting embedding helper script
2025-04-05 08:58:00,612 - INFO - Reading input from stdin
2025-04-05 08:58:00,613 - INFO - Input data length: 1171
2025-04-05 08:58:00,613 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=980
2025-04-05 08:58:00,613 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:58:00,614 - INFO - Text length: 980
2025-04-05 08:58:02,843 - INFO - Torch threads set to 1
2025-04-05 08:58:06,923 - INFO - Loading model...
2025-04-05 08:58:06,924 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:58:12,161 - INFO - Model loaded successfully
2025-04-05 08:58:12,163 - INFO - Generating embedding...
2025-04-05 08:58:12,307 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:58:12,308 - INFO - Returning successful result
2025-04-05 08:58:12,308 - INFO - Writing result to stdout
2025-04-05 08:58:12,309 - INFO - Result written successfully
2025-04-05 08:58:13,219 - INFO - Starting embedding helper script
2025-04-05 08:58:13,220 - INFO - Reading input from stdin
2025-04-05 08:58:13,220 - INFO - Input data length: 971
2025-04-05 08:58:13,220 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=920
2025-04-05 08:58:13,221 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:58:13,221 - INFO - Text length: 920
2025-04-05 08:58:15,627 - INFO - Torch threads set to 1
2025-04-05 08:58:19,923 - INFO - Loading model...
2025-04-05 08:58:19,924 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:58:25,268 - INFO - Model loaded successfully
2025-04-05 08:58:25,268 - INFO - Generating embedding...
2025-04-05 08:58:25,440 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:58:25,441 - INFO - Returning successful result
2025-04-05 08:58:25,441 - INFO - Writing result to stdout
2025-04-05 08:58:25,442 - INFO - Result written successfully
2025-04-05 08:58:26,418 - INFO - Starting embedding helper script
2025-04-05 08:58:26,418 - INFO - Reading input from stdin
2025-04-05 08:58:26,419 - INFO - Input data length: 949
2025-04-05 08:58:26,419 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=903
2025-04-05 08:58:26,419 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:58:26,420 - INFO - Text length: 903
2025-04-05 08:58:28,749 - INFO - Torch threads set to 1
2025-04-05 08:58:32,935 - INFO - Loading model...
2025-04-05 08:58:32,936 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:58:37,721 - INFO - Model loaded successfully
2025-04-05 08:58:37,721 - INFO - Generating embedding...
2025-04-05 08:58:37,897 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:58:37,897 - INFO - Returning successful result
2025-04-05 08:58:37,898 - INFO - Writing result to stdout
2025-04-05 08:58:37,898 - INFO - Result written successfully
2025-04-05 08:58:39,030 - INFO - Starting embedding helper script
2025-04-05 08:58:39,031 - INFO - Reading input from stdin
2025-04-05 08:58:39,031 - INFO - Input data length: 1041
2025-04-05 08:58:39,032 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=994
2025-04-05 08:58:39,032 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:58:39,032 - INFO - Text length: 994
2025-04-05 08:58:41,894 - INFO - Torch threads set to 1
2025-04-05 08:58:46,133 - INFO - Loading model...
2025-04-05 08:58:46,135 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:58:50,675 - INFO - Model loaded successfully
2025-04-05 08:58:50,675 - INFO - Generating embedding...
2025-04-05 08:58:50,810 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:58:50,811 - INFO - Returning successful result
2025-04-05 08:58:50,812 - INFO - Writing result to stdout
2025-04-05 08:58:50,812 - INFO - Result written successfully
2025-04-05 08:58:51,739 - INFO - Starting embedding helper script
2025-04-05 08:58:51,739 - INFO - Reading input from stdin
2025-04-05 08:58:51,739 - INFO - Input data length: 988
2025-04-05 08:58:51,740 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=942
2025-04-05 08:58:51,740 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:58:51,740 - INFO - Text length: 942
2025-04-05 08:58:54,199 - INFO - Torch threads set to 1
2025-04-05 08:58:59,484 - INFO - Loading model...
2025-04-05 08:58:59,486 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:59:04,383 - INFO - Model loaded successfully
2025-04-05 08:59:04,383 - INFO - Generating embedding...
2025-04-05 08:59:04,535 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:59:04,536 - INFO - Returning successful result
2025-04-05 08:59:04,536 - INFO - Writing result to stdout
2025-04-05 08:59:04,537 - INFO - Result written successfully
2025-04-05 08:59:05,472 - INFO - Starting embedding helper script
2025-04-05 08:59:05,473 - INFO - Reading input from stdin
2025-04-05 08:59:05,473 - INFO - Input data length: 1045
2025-04-05 08:59:05,473 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=999
2025-04-05 08:59:05,474 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:59:05,474 - INFO - Text length: 999
2025-04-05 08:59:07,584 - INFO - Torch threads set to 1
2025-04-05 08:59:12,351 - INFO - Loading model...
2025-04-05 08:59:12,353 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:59:17,389 - INFO - Model loaded successfully
2025-04-05 08:59:17,389 - INFO - Generating embedding...
2025-04-05 08:59:17,584 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:59:17,585 - INFO - Returning successful result
2025-04-05 08:59:17,585 - INFO - Writing result to stdout
2025-04-05 08:59:17,585 - INFO - Result written successfully
2025-04-05 08:59:18,629 - INFO - Starting embedding helper script
2025-04-05 08:59:18,629 - INFO - Reading input from stdin
2025-04-05 08:59:18,630 - INFO - Input data length: 1022
2025-04-05 08:59:18,630 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=976
2025-04-05 08:59:18,630 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:59:18,631 - INFO - Text length: 976
2025-04-05 08:59:21,609 - INFO - Torch threads set to 1
2025-04-05 08:59:28,636 - INFO - Loading model...
2025-04-05 08:59:28,637 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:59:34,284 - INFO - Model loaded successfully
2025-04-05 08:59:34,284 - INFO - Generating embedding...
2025-04-05 08:59:34,482 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:59:34,483 - INFO - Returning successful result
2025-04-05 08:59:34,484 - INFO - Writing result to stdout
2025-04-05 08:59:34,484 - INFO - Result written successfully
2025-04-05 08:59:35,497 - INFO - Starting embedding helper script
2025-04-05 08:59:35,498 - INFO - Reading input from stdin
2025-04-05 08:59:35,498 - INFO - Input data length: 932
2025-04-05 08:59:35,498 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=886
2025-04-05 08:59:35,499 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:59:35,499 - INFO - Text length: 886
2025-04-05 08:59:37,561 - INFO - Torch threads set to 1
2025-04-05 08:59:41,471 - INFO - Loading model...
2025-04-05 08:59:41,472 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:59:46,773 - INFO - Model loaded successfully
2025-04-05 08:59:46,774 - INFO - Generating embedding...
2025-04-05 08:59:46,924 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:59:46,925 - INFO - Returning successful result
2025-04-05 08:59:46,925 - INFO - Writing result to stdout
2025-04-05 08:59:46,926 - INFO - Result written successfully
2025-04-05 08:59:47,839 - INFO - Starting embedding helper script
2025-04-05 08:59:47,839 - INFO - Reading input from stdin
2025-04-05 08:59:47,839 - INFO - Input data length: 1019
2025-04-05 08:59:47,840 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=973
2025-04-05 08:59:47,840 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 08:59:47,840 - INFO - Text length: 973
2025-04-05 08:59:49,816 - INFO - Torch threads set to 1
2025-04-05 08:59:53,723 - INFO - Loading model...
2025-04-05 08:59:53,724 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 08:59:59,536 - INFO - Model loaded successfully
2025-04-05 08:59:59,536 - INFO - Generating embedding...
2025-04-05 08:59:59,675 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 08:59:59,675 - INFO - Returning successful result
2025-04-05 08:59:59,676 - INFO - Writing result to stdout
2025-04-05 08:59:59,676 - INFO - Result written successfully
2025-04-05 09:00:00,628 - INFO - Starting embedding helper script
2025-04-05 09:00:00,635 - INFO - Reading input from stdin
2025-04-05 09:00:00,636 - INFO - Input data length: 1021
2025-04-05 09:00:00,637 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=975
2025-04-05 09:00:00,637 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:00:00,637 - INFO - Text length: 975
2025-04-05 09:00:02,525 - INFO - Torch threads set to 1
2025-04-05 09:00:06,686 - INFO - Loading model...
2025-04-05 09:00:06,687 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:00:11,413 - INFO - Model loaded successfully
2025-04-05 09:00:11,413 - INFO - Generating embedding...
2025-04-05 09:00:11,548 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:00:11,549 - INFO - Returning successful result
2025-04-05 09:00:11,549 - INFO - Writing result to stdout
2025-04-05 09:00:11,549 - INFO - Result written successfully
2025-04-05 09:00:12,555 - INFO - Starting embedding helper script
2025-04-05 09:00:12,555 - INFO - Reading input from stdin
2025-04-05 09:00:12,556 - INFO - Input data length: 998
2025-04-05 09:00:12,556 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=952
2025-04-05 09:00:12,556 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:00:12,557 - INFO - Text length: 952
2025-04-05 09:00:16,764 - INFO - Torch threads set to 1
2025-04-05 09:00:22,198 - INFO - Loading model...
2025-04-05 09:00:22,199 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:00:27,189 - INFO - Model loaded successfully
2025-04-05 09:00:27,190 - INFO - Generating embedding...
2025-04-05 09:00:27,433 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:00:27,434 - INFO - Returning successful result
2025-04-05 09:00:27,435 - INFO - Writing result to stdout
2025-04-05 09:00:27,435 - INFO - Result written successfully
2025-04-05 09:00:29,089 - INFO - Starting embedding helper script
2025-04-05 09:00:29,090 - INFO - Reading input from stdin
2025-04-05 09:00:29,090 - INFO - Input data length: 971
2025-04-05 09:00:29,091 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=925
2025-04-05 09:00:29,091 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:00:29,091 - INFO - Text length: 925
2025-04-05 09:00:31,251 - INFO - Torch threads set to 1
2025-04-05 09:00:35,513 - INFO - Loading model...
2025-04-05 09:00:35,514 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:00:42,073 - INFO - Model loaded successfully
2025-04-05 09:00:42,073 - INFO - Generating embedding...
2025-04-05 09:00:42,240 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:00:42,241 - INFO - Returning successful result
2025-04-05 09:00:42,241 - INFO - Writing result to stdout
2025-04-05 09:00:42,241 - INFO - Result written successfully
2025-04-05 09:00:43,766 - INFO - Starting embedding helper script
2025-04-05 09:00:43,767 - INFO - Reading input from stdin
2025-04-05 09:00:43,767 - INFO - Input data length: 948
2025-04-05 09:00:43,767 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=902
2025-04-05 09:00:43,768 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:00:43,768 - INFO - Text length: 902
2025-04-05 09:00:47,466 - INFO - Torch threads set to 1
2025-04-05 09:00:52,753 - INFO - Loading model...
2025-04-05 09:00:52,757 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:00:57,849 - INFO - Model loaded successfully
2025-04-05 09:00:57,849 - INFO - Generating embedding...
2025-04-05 09:00:58,006 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:00:58,007 - INFO - Returning successful result
2025-04-05 09:00:58,007 - INFO - Writing result to stdout
2025-04-05 09:00:58,007 - INFO - Result written successfully
2025-04-05 09:00:59,111 - INFO - Starting embedding helper script
2025-04-05 09:00:59,112 - INFO - Reading input from stdin
2025-04-05 09:00:59,112 - INFO - Input data length: 1020
2025-04-05 09:00:59,112 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=974
2025-04-05 09:00:59,113 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:00:59,113 - INFO - Text length: 974
2025-04-05 09:01:01,385 - INFO - Torch threads set to 1
2025-04-05 09:01:05,932 - INFO - Loading model...
2025-04-05 09:01:05,933 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:01:09,983 - INFO - Model loaded successfully
2025-04-05 09:01:09,984 - INFO - Generating embedding...
2025-04-05 09:01:10,148 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:01:10,149 - INFO - Returning successful result
2025-04-05 09:01:10,150 - INFO - Writing result to stdout
2025-04-05 09:01:10,150 - INFO - Result written successfully
2025-04-05 09:01:11,267 - INFO - Starting embedding helper script
2025-04-05 09:01:11,268 - INFO - Reading input from stdin
2025-04-05 09:01:11,268 - INFO - Input data length: 1012
2025-04-05 09:01:11,269 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=966
2025-04-05 09:01:11,269 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:01:11,269 - INFO - Text length: 966
2025-04-05 09:01:13,564 - INFO - Torch threads set to 1
2025-04-05 09:01:18,106 - INFO - Loading model...
2025-04-05 09:01:18,107 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:01:23,152 - INFO - Model loaded successfully
2025-04-05 09:01:23,152 - INFO - Generating embedding...
2025-04-05 09:01:23,323 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:01:23,324 - INFO - Returning successful result
2025-04-05 09:01:23,325 - INFO - Writing result to stdout
2025-04-05 09:01:23,325 - INFO - Result written successfully
2025-04-05 09:01:24,394 - INFO - Starting embedding helper script
2025-04-05 09:01:24,395 - INFO - Reading input from stdin
2025-04-05 09:01:24,395 - INFO - Input data length: 971
2025-04-05 09:01:24,396 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=925
2025-04-05 09:01:24,396 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:01:24,396 - INFO - Text length: 925
2025-04-05 09:01:26,547 - INFO - Torch threads set to 1
2025-04-05 09:01:32,369 - INFO - Loading model...
2025-04-05 09:01:32,370 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:01:37,562 - INFO - Model loaded successfully
2025-04-05 09:01:37,562 - INFO - Generating embedding...
2025-04-05 09:01:37,723 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:01:37,723 - INFO - Returning successful result
2025-04-05 09:01:37,724 - INFO - Writing result to stdout
2025-04-05 09:01:37,724 - INFO - Result written successfully
2025-04-05 09:01:38,896 - INFO - Starting embedding helper script
2025-04-05 09:01:38,896 - INFO - Reading input from stdin
2025-04-05 09:01:38,897 - INFO - Input data length: 946
2025-04-05 09:01:38,897 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=900
2025-04-05 09:01:38,897 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:01:38,898 - INFO - Text length: 900
2025-04-05 09:01:41,096 - INFO - Torch threads set to 1
2025-04-05 09:01:45,662 - INFO - Loading model...
2025-04-05 09:01:45,664 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:01:50,171 - INFO - Model loaded successfully
2025-04-05 09:01:50,171 - INFO - Generating embedding...
2025-04-05 09:01:50,373 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:01:50,374 - INFO - Returning successful result
2025-04-05 09:01:50,374 - INFO - Writing result to stdout
2025-04-05 09:01:50,375 - INFO - Result written successfully
2025-04-05 09:01:51,410 - INFO - Starting embedding helper script
2025-04-05 09:01:51,410 - INFO - Reading input from stdin
2025-04-05 09:01:51,410 - INFO - Input data length: 1008
2025-04-05 09:01:51,411 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=962
2025-04-05 09:01:51,411 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:01:51,411 - INFO - Text length: 962
2025-04-05 09:01:53,651 - INFO - Torch threads set to 1
2025-04-05 09:01:57,790 - INFO - Loading model...
2025-04-05 09:01:57,791 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:02:02,661 - INFO - Model loaded successfully
2025-04-05 09:02:02,662 - INFO - Generating embedding...
2025-04-05 09:02:02,809 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:02:02,810 - INFO - Returning successful result
2025-04-05 09:02:02,810 - INFO - Writing result to stdout
2025-04-05 09:02:02,810 - INFO - Result written successfully
2025-04-05 09:02:03,835 - INFO - Starting embedding helper script
2025-04-05 09:02:03,836 - INFO - Reading input from stdin
2025-04-05 09:02:03,836 - INFO - Input data length: 1002
2025-04-05 09:02:03,836 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=956
2025-04-05 09:02:03,837 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:02:03,837 - INFO - Text length: 956
2025-04-05 09:02:05,968 - INFO - Torch threads set to 1
2025-04-05 09:02:10,128 - INFO - Loading model...
2025-04-05 09:02:10,130 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:02:14,541 - INFO - Model loaded successfully
2025-04-05 09:02:14,541 - INFO - Generating embedding...
2025-04-05 09:02:14,688 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:02:14,689 - INFO - Returning successful result
2025-04-05 09:02:14,689 - INFO - Writing result to stdout
2025-04-05 09:02:14,689 - INFO - Result written successfully
2025-04-05 09:02:15,692 - INFO - Starting embedding helper script
2025-04-05 09:02:15,692 - INFO - Reading input from stdin
2025-04-05 09:02:15,692 - INFO - Input data length: 918
2025-04-05 09:02:15,693 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=872
2025-04-05 09:02:15,693 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:02:15,694 - INFO - Text length: 872
2025-04-05 09:02:18,089 - INFO - Torch threads set to 1
2025-04-05 09:02:22,548 - INFO - Loading model...
2025-04-05 09:02:22,551 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:02:26,644 - INFO - Model loaded successfully
2025-04-05 09:02:26,644 - INFO - Generating embedding...
2025-04-05 09:02:26,796 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:02:26,797 - INFO - Returning successful result
2025-04-05 09:02:26,797 - INFO - Writing result to stdout
2025-04-05 09:02:26,798 - INFO - Result written successfully
2025-04-05 09:02:27,827 - INFO - Starting embedding helper script
2025-04-05 09:02:27,828 - INFO - Reading input from stdin
2025-04-05 09:02:27,828 - INFO - Input data length: 1032
2025-04-05 09:02:27,828 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=986
2025-04-05 09:02:27,829 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:02:27,829 - INFO - Text length: 986
2025-04-05 09:02:30,488 - INFO - Torch threads set to 1
2025-04-05 09:02:35,914 - INFO - Loading model...
2025-04-05 09:02:35,915 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:02:40,038 - INFO - Model loaded successfully
2025-04-05 09:02:40,038 - INFO - Generating embedding...
2025-04-05 09:02:40,191 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:02:40,192 - INFO - Returning successful result
2025-04-05 09:02:40,192 - INFO - Writing result to stdout
2025-04-05 09:02:40,192 - INFO - Result written successfully
2025-04-05 09:02:41,362 - INFO - Starting embedding helper script
2025-04-05 09:02:41,362 - INFO - Reading input from stdin
2025-04-05 09:02:41,362 - INFO - Input data length: 1043
2025-04-05 09:02:41,363 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=997
2025-04-05 09:02:41,363 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:02:41,364 - INFO - Text length: 997
2025-04-05 09:02:43,274 - INFO - Torch threads set to 1
2025-04-05 09:02:47,549 - INFO - Loading model...
2025-04-05 09:02:47,550 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:02:51,874 - INFO - Model loaded successfully
2025-04-05 09:02:51,875 - INFO - Generating embedding...
2025-04-05 09:02:52,005 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:02:52,006 - INFO - Returning successful result
2025-04-05 09:02:52,006 - INFO - Writing result to stdout
2025-04-05 09:02:52,007 - INFO - Result written successfully
2025-04-05 09:02:52,992 - INFO - Starting embedding helper script
2025-04-05 09:02:52,992 - INFO - Reading input from stdin
2025-04-05 09:02:52,993 - INFO - Input data length: 1004
2025-04-05 09:02:52,993 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=958
2025-04-05 09:02:52,993 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:02:52,994 - INFO - Text length: 958
2025-04-05 09:02:56,700 - INFO - Torch threads set to 1
2025-04-05 09:03:01,248 - INFO - Loading model...
2025-04-05 09:03:01,249 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:03:05,146 - INFO - Model loaded successfully
2025-04-05 09:03:05,147 - INFO - Generating embedding...
2025-04-05 09:03:05,292 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:03:05,293 - INFO - Returning successful result
2025-04-05 09:03:05,296 - INFO - Writing result to stdout
2025-04-05 09:03:05,298 - INFO - Result written successfully
2025-04-05 09:03:06,328 - INFO - Starting embedding helper script
2025-04-05 09:03:06,328 - INFO - Reading input from stdin
2025-04-05 09:03:06,328 - INFO - Input data length: 1003
2025-04-05 09:03:06,329 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=957
2025-04-05 09:03:06,329 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:03:06,330 - INFO - Text length: 957
2025-04-05 09:03:08,485 - INFO - Torch threads set to 1
2025-04-05 09:03:12,628 - INFO - Loading model...
2025-04-05 09:03:12,629 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:03:16,050 - INFO - Model loaded successfully
2025-04-05 09:03:16,051 - INFO - Generating embedding...
2025-04-05 09:03:16,198 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:03:16,199 - INFO - Returning successful result
2025-04-05 09:03:16,200 - INFO - Writing result to stdout
2025-04-05 09:03:16,200 - INFO - Result written successfully
2025-04-05 09:03:17,226 - INFO - Starting embedding helper script
2025-04-05 09:03:17,226 - INFO - Reading input from stdin
2025-04-05 09:03:17,227 - INFO - Input data length: 1039
2025-04-05 09:03:17,227 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=993
2025-04-05 09:03:17,227 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:03:17,228 - INFO - Text length: 993
2025-04-05 09:03:19,294 - INFO - Torch threads set to 1
2025-04-05 09:03:23,425 - INFO - Loading model...
2025-04-05 09:03:23,426 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:03:28,732 - INFO - Model loaded successfully
2025-04-05 09:03:28,732 - INFO - Generating embedding...
2025-04-05 09:03:28,883 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:03:28,884 - INFO - Returning successful result
2025-04-05 09:03:28,885 - INFO - Writing result to stdout
2025-04-05 09:03:28,885 - INFO - Result written successfully
2025-04-05 09:03:29,916 - INFO - Starting embedding helper script
2025-04-05 09:03:29,916 - INFO - Reading input from stdin
2025-04-05 09:03:29,916 - INFO - Input data length: 814
2025-04-05 09:03:29,917 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=768
2025-04-05 09:03:29,917 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:03:29,918 - INFO - Text length: 768
2025-04-05 09:03:31,947 - INFO - Torch threads set to 1
2025-04-05 09:03:36,154 - INFO - Loading model...
2025-04-05 09:03:36,155 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:03:41,889 - INFO - Model loaded successfully
2025-04-05 09:03:41,890 - INFO - Generating embedding...
2025-04-05 09:03:42,024 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:03:42,025 - INFO - Returning successful result
2025-04-05 09:03:42,025 - INFO - Writing result to stdout
2025-04-05 09:03:42,026 - INFO - Result written successfully
2025-04-05 09:03:43,029 - INFO - Starting embedding helper script
2025-04-05 09:03:43,029 - INFO - Reading input from stdin
2025-04-05 09:03:43,030 - INFO - Input data length: 1009
2025-04-05 09:03:43,030 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=963
2025-04-05 09:03:43,030 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:03:43,031 - INFO - Text length: 963
2025-04-05 09:03:45,126 - INFO - Torch threads set to 1
2025-04-05 09:03:49,227 - INFO - Loading model...
2025-04-05 09:03:49,229 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:03:55,099 - INFO - Model loaded successfully
2025-04-05 09:03:55,099 - INFO - Generating embedding...
2025-04-05 09:03:55,246 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:03:55,247 - INFO - Returning successful result
2025-04-05 09:03:55,247 - INFO - Writing result to stdout
2025-04-05 09:03:55,247 - INFO - Result written successfully
2025-04-05 09:03:56,267 - INFO - Starting embedding helper script
2025-04-05 09:03:56,267 - INFO - Reading input from stdin
2025-04-05 09:03:56,268 - INFO - Input data length: 1038
2025-04-05 09:03:56,268 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=992
2025-04-05 09:03:56,268 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:03:56,269 - INFO - Text length: 992
2025-04-05 09:03:58,330 - INFO - Torch threads set to 1
2025-04-05 09:04:02,446 - INFO - Loading model...
2025-04-05 09:04:02,448 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:04:08,309 - INFO - Model loaded successfully
2025-04-05 09:04:08,309 - INFO - Generating embedding...
2025-04-05 09:04:08,448 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:04:08,449 - INFO - Returning successful result
2025-04-05 09:04:08,449 - INFO - Writing result to stdout
2025-04-05 09:04:08,449 - INFO - Result written successfully
2025-04-05 09:04:09,462 - INFO - Starting embedding helper script
2025-04-05 09:04:09,463 - INFO - Reading input from stdin
2025-04-05 09:04:09,463 - INFO - Input data length: 1016
2025-04-05 09:04:09,463 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=970
2025-04-05 09:04:09,464 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:04:09,464 - INFO - Text length: 970
2025-04-05 09:04:11,581 - INFO - Torch threads set to 1
2025-04-05 09:04:15,833 - INFO - Loading model...
2025-04-05 09:04:15,835 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:04:21,225 - INFO - Model loaded successfully
2025-04-05 09:04:21,226 - INFO - Generating embedding...
2025-04-05 09:04:21,379 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:04:21,380 - INFO - Returning successful result
2025-04-05 09:04:21,380 - INFO - Writing result to stdout
2025-04-05 09:04:21,380 - INFO - Result written successfully
2025-04-05 09:04:22,488 - INFO - Starting embedding helper script
2025-04-05 09:04:22,488 - INFO - Reading input from stdin
2025-04-05 09:04:22,488 - INFO - Input data length: 1006
2025-04-05 09:04:22,489 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=960
2025-04-05 09:04:22,489 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:04:22,489 - INFO - Text length: 960
2025-04-05 09:04:24,801 - INFO - Torch threads set to 1
2025-04-05 09:04:29,099 - INFO - Loading model...
2025-04-05 09:04:29,100 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:04:34,833 - INFO - Model loaded successfully
2025-04-05 09:04:34,834 - INFO - Generating embedding...
2025-04-05 09:04:34,996 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:04:34,997 - INFO - Returning successful result
2025-04-05 09:04:34,997 - INFO - Writing result to stdout
2025-04-05 09:04:34,998 - INFO - Result written successfully
2025-04-05 09:04:36,466 - INFO - Starting embedding helper script
2025-04-05 09:04:36,466 - INFO - Reading input from stdin
2025-04-05 09:04:36,466 - INFO - Input data length: 1023
2025-04-05 09:04:36,467 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=977
2025-04-05 09:04:36,467 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:04:36,467 - INFO - Text length: 977
2025-04-05 09:04:41,547 - INFO - Torch threads set to 1
2025-04-05 09:04:46,091 - INFO - Loading model...
2025-04-05 09:04:46,092 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:04:53,071 - INFO - Model loaded successfully
2025-04-05 09:04:53,072 - INFO - Generating embedding...
2025-04-05 09:04:53,279 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:04:53,280 - INFO - Returning successful result
2025-04-05 09:04:53,281 - INFO - Writing result to stdout
2025-04-05 09:04:53,281 - INFO - Result written successfully
2025-04-05 09:04:54,614 - INFO - Starting embedding helper script
2025-04-05 09:04:54,615 - INFO - Reading input from stdin
2025-04-05 09:04:54,615 - INFO - Input data length: 1040
2025-04-05 09:04:54,615 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=994
2025-04-05 09:04:54,616 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:04:54,616 - INFO - Text length: 994
2025-04-05 09:04:56,899 - INFO - Torch threads set to 1
2025-04-05 09:05:01,079 - INFO - Loading model...
2025-04-05 09:05:01,080 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:05:06,703 - INFO - Model loaded successfully
2025-04-05 09:05:06,703 - INFO - Generating embedding...
2025-04-05 09:05:06,859 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:05:06,860 - INFO - Returning successful result
2025-04-05 09:05:06,861 - INFO - Writing result to stdout
2025-04-05 09:05:06,861 - INFO - Result written successfully
2025-04-05 09:05:07,889 - INFO - Starting embedding helper script
2025-04-05 09:05:07,890 - INFO - Reading input from stdin
2025-04-05 09:05:07,890 - INFO - Input data length: 969
2025-04-05 09:05:07,890 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=923
2025-04-05 09:05:07,891 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:05:07,891 - INFO - Text length: 923
2025-04-05 09:05:10,283 - INFO - Torch threads set to 1
2025-04-05 09:05:15,074 - INFO - Loading model...
2025-04-05 09:05:15,075 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:05:20,689 - INFO - Model loaded successfully
2025-04-05 09:05:20,689 - INFO - Generating embedding...
2025-04-05 09:05:20,848 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:05:20,849 - INFO - Returning successful result
2025-04-05 09:05:20,850 - INFO - Writing result to stdout
2025-04-05 09:05:20,850 - INFO - Result written successfully
2025-04-05 09:05:22,038 - INFO - Starting embedding helper script
2025-04-05 09:05:22,038 - INFO - Reading input from stdin
2025-04-05 09:05:22,038 - INFO - Input data length: 966
2025-04-05 09:05:22,039 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=920
2025-04-05 09:05:22,039 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:05:22,039 - INFO - Text length: 920
2025-04-05 09:05:24,371 - INFO - Torch threads set to 1
2025-04-05 09:05:29,502 - INFO - Loading model...
2025-04-05 09:05:29,503 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:05:35,048 - INFO - Model loaded successfully
2025-04-05 09:05:35,048 - INFO - Generating embedding...
2025-04-05 09:05:35,310 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:05:35,314 - INFO - Returning successful result
2025-04-05 09:05:35,314 - INFO - Writing result to stdout
2025-04-05 09:05:35,315 - INFO - Result written successfully
2025-04-05 09:05:36,720 - INFO - Starting embedding helper script
2025-04-05 09:05:36,721 - INFO - Reading input from stdin
2025-04-05 09:05:36,721 - INFO - Input data length: 1027
2025-04-05 09:05:36,721 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=981
2025-04-05 09:05:36,722 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:05:36,722 - INFO - Text length: 981
2025-04-05 09:05:39,220 - INFO - Torch threads set to 1
2025-04-05 09:05:43,781 - INFO - Loading model...
2025-04-05 09:05:43,782 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:05:48,807 - INFO - Model loaded successfully
2025-04-05 09:05:48,808 - INFO - Generating embedding...
2025-04-05 09:05:48,958 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:05:48,960 - INFO - Returning successful result
2025-04-05 09:05:48,960 - INFO - Writing result to stdout
2025-04-05 09:05:48,961 - INFO - Result written successfully
2025-04-05 09:05:50,048 - INFO - Starting embedding helper script
2025-04-05 09:05:50,048 - INFO - Reading input from stdin
2025-04-05 09:05:50,048 - INFO - Input data length: 1041
2025-04-05 09:05:50,049 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=975
2025-04-05 09:05:50,049 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:05:50,050 - INFO - Text length: 975
2025-04-05 09:05:52,251 - INFO - Torch threads set to 1
2025-04-05 09:05:57,113 - INFO - Loading model...
2025-04-05 09:05:57,129 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:06:02,628 - INFO - Model loaded successfully
2025-04-05 09:06:02,628 - INFO - Generating embedding...
2025-04-05 09:06:02,765 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:06:02,766 - INFO - Returning successful result
2025-04-05 09:06:02,766 - INFO - Writing result to stdout
2025-04-05 09:06:02,767 - INFO - Result written successfully
2025-04-05 09:06:03,823 - INFO - Starting embedding helper script
2025-04-05 09:06:03,824 - INFO - Reading input from stdin
2025-04-05 09:06:03,824 - INFO - Input data length: 966
2025-04-05 09:06:03,825 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=915
2025-04-05 09:06:03,825 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:06:03,825 - INFO - Text length: 915
2025-04-05 09:06:06,099 - INFO - Torch threads set to 1
2025-04-05 09:06:10,570 - INFO - Loading model...
2025-04-05 09:06:10,571 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:06:16,274 - INFO - Model loaded successfully
2025-04-05 09:06:16,274 - INFO - Generating embedding...
2025-04-05 09:06:16,417 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:06:16,418 - INFO - Returning successful result
2025-04-05 09:06:16,419 - INFO - Writing result to stdout
2025-04-05 09:06:16,419 - INFO - Result written successfully
2025-04-05 09:06:17,447 - INFO - Starting embedding helper script
2025-04-05 09:06:17,447 - INFO - Reading input from stdin
2025-04-05 09:06:17,448 - INFO - Input data length: 999
2025-04-05 09:06:17,448 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=953
2025-04-05 09:06:17,449 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:06:17,449 - INFO - Text length: 953
2025-04-05 09:06:19,505 - INFO - Torch threads set to 1
2025-04-05 09:06:24,108 - INFO - Loading model...
2025-04-05 09:06:24,113 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:06:29,416 - INFO - Model loaded successfully
2025-04-05 09:06:29,417 - INFO - Generating embedding...
2025-04-05 09:06:29,564 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:06:29,565 - INFO - Returning successful result
2025-04-05 09:06:29,566 - INFO - Writing result to stdout
2025-04-05 09:06:29,566 - INFO - Result written successfully
2025-04-05 09:06:30,580 - INFO - Starting embedding helper script
2025-04-05 09:06:30,580 - INFO - Reading input from stdin
2025-04-05 09:06:30,580 - INFO - Input data length: 1041
2025-04-05 09:06:30,581 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=995
2025-04-05 09:06:30,581 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:06:30,581 - INFO - Text length: 995
2025-04-05 09:06:32,790 - INFO - Torch threads set to 1
2025-04-05 09:06:37,321 - INFO - Loading model...
2025-04-05 09:06:37,323 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:06:42,728 - INFO - Model loaded successfully
2025-04-05 09:06:42,728 - INFO - Generating embedding...
2025-04-05 09:06:42,944 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:06:42,945 - INFO - Returning successful result
2025-04-05 09:06:42,945 - INFO - Writing result to stdout
2025-04-05 09:06:42,945 - INFO - Result written successfully
2025-04-05 09:06:43,991 - INFO - Starting embedding helper script
2025-04-05 09:06:43,992 - INFO - Reading input from stdin
2025-04-05 09:06:43,992 - INFO - Input data length: 1027
2025-04-05 09:06:43,992 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=981
2025-04-05 09:06:43,993 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:06:43,993 - INFO - Text length: 981
2025-04-05 09:06:46,119 - INFO - Torch threads set to 1
2025-04-05 09:06:50,437 - INFO - Loading model...
2025-04-05 09:06:50,438 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:06:55,323 - INFO - Model loaded successfully
2025-04-05 09:06:55,324 - INFO - Generating embedding...
2025-04-05 09:06:55,469 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:06:55,470 - INFO - Returning successful result
2025-04-05 09:06:55,470 - INFO - Writing result to stdout
2025-04-05 09:06:55,472 - INFO - Result written successfully
2025-04-05 09:06:56,498 - INFO - Starting embedding helper script
2025-04-05 09:06:56,498 - INFO - Reading input from stdin
2025-04-05 09:06:56,499 - INFO - Input data length: 1038
2025-04-05 09:06:56,499 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=987
2025-04-05 09:06:56,500 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:06:56,500 - INFO - Text length: 987
2025-04-05 09:06:58,970 - INFO - Torch threads set to 1
2025-04-05 09:07:03,161 - INFO - Loading model...
2025-04-05 09:07:03,162 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:07:08,378 - INFO - Model loaded successfully
2025-04-05 09:07:08,379 - INFO - Generating embedding...
2025-04-05 09:07:08,522 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:07:08,523 - INFO - Returning successful result
2025-04-05 09:07:08,524 - INFO - Writing result to stdout
2025-04-05 09:07:08,524 - INFO - Result written successfully
2025-04-05 09:07:09,548 - INFO - Starting embedding helper script
2025-04-05 09:07:09,549 - INFO - Reading input from stdin
2025-04-05 09:07:09,549 - INFO - Input data length: 1000
2025-04-05 09:07:09,549 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=934
2025-04-05 09:07:09,550 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:07:09,550 - INFO - Text length: 934
2025-04-05 09:07:11,599 - INFO - Torch threads set to 1
2025-04-05 09:07:15,803 - INFO - Loading model...
2025-04-05 09:07:15,804 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:07:20,600 - INFO - Model loaded successfully
2025-04-05 09:07:20,600 - INFO - Generating embedding...
2025-04-05 09:07:20,737 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:07:20,738 - INFO - Returning successful result
2025-04-05 09:07:20,738 - INFO - Writing result to stdout
2025-04-05 09:07:20,739 - INFO - Result written successfully
2025-04-05 09:07:21,755 - INFO - Starting embedding helper script
2025-04-05 09:07:21,756 - INFO - Reading input from stdin
2025-04-05 09:07:21,756 - INFO - Input data length: 712
2025-04-05 09:07:21,757 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=651
2025-04-05 09:07:21,757 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:07:21,757 - INFO - Text length: 651
2025-04-05 09:07:24,061 - INFO - Torch threads set to 1
2025-04-05 09:07:28,684 - INFO - Loading model...
2025-04-05 09:07:28,685 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:07:34,407 - INFO - Model loaded successfully
2025-04-05 09:07:34,407 - INFO - Generating embedding...
2025-04-05 09:07:34,542 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:07:34,543 - INFO - Returning successful result
2025-04-05 09:07:34,544 - INFO - Writing result to stdout
2025-04-05 09:07:34,545 - INFO - Result written successfully
2025-04-05 09:58:34,557 - INFO - Starting embedding helper script
2025-04-05 09:58:34,558 - INFO - Reading input from stdin
2025-04-05 09:58:34,558 - INFO - Input data length: 2036
2025-04-05 09:58:34,559 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1990
2025-04-05 09:58:34,559 - INFO - Processing text with length: 1990 chars
2025-04-05 09:58:34,561 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:58:34,561 - INFO - Text length: 1990
2025-04-05 09:58:37,975 - INFO - Torch threads set to 1
2025-04-05 09:58:43,918 - INFO - Loading model...
2025-04-05 09:58:43,919 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:58:49,124 - INFO - Model loaded successfully
2025-04-05 09:58:49,125 - INFO - Generating embedding...
2025-04-05 09:58:50,383 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:58:50,385 - INFO - Returning successful result
2025-04-05 09:58:50,385 - INFO - Writing result to stdout
2025-04-05 09:58:50,386 - INFO - Result written successfully
2025-04-05 09:58:51,713 - INFO - Starting embedding helper script
2025-04-05 09:58:51,714 - INFO - Reading input from stdin
2025-04-05 09:58:51,714 - INFO - Input data length: 1732
2025-04-05 09:58:51,714 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1686
2025-04-05 09:58:51,715 - INFO - Processing text with length: 1686 chars
2025-04-05 09:58:51,715 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:58:51,715 - INFO - Text length: 1686
2025-04-05 09:58:54,425 - INFO - Torch threads set to 1
2025-04-05 09:58:59,440 - INFO - Loading model...
2025-04-05 09:58:59,441 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:59:04,384 - INFO - Model loaded successfully
2025-04-05 09:59:04,385 - INFO - Generating embedding...
2025-04-05 09:59:04,622 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:59:04,623 - INFO - Returning successful result
2025-04-05 09:59:04,623 - INFO - Writing result to stdout
2025-04-05 09:59:04,623 - INFO - Result written successfully
2025-04-05 09:59:06,014 - INFO - Starting embedding helper script
2025-04-05 09:59:06,014 - INFO - Reading input from stdin
2025-04-05 09:59:06,015 - INFO - Input data length: 1949
2025-04-05 09:59:06,015 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1903
2025-04-05 09:59:06,015 - INFO - Processing text with length: 1903 chars
2025-04-05 09:59:06,015 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:59:06,016 - INFO - Text length: 1903
2025-04-05 09:59:08,621 - INFO - Torch threads set to 1
2025-04-05 09:59:13,838 - INFO - Loading model...
2025-04-05 09:59:13,839 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:59:19,127 - INFO - Model loaded successfully
2025-04-05 09:59:19,128 - INFO - Generating embedding...
2025-04-05 09:59:19,301 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:59:19,303 - INFO - Returning successful result
2025-04-05 09:59:19,306 - INFO - Writing result to stdout
2025-04-05 09:59:19,306 - INFO - Result written successfully
2025-04-05 09:59:20,444 - INFO - Starting embedding helper script
2025-04-05 09:59:20,444 - INFO - Reading input from stdin
2025-04-05 09:59:20,444 - INFO - Input data length: 1506
2025-04-05 09:59:20,445 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1460
2025-04-05 09:59:20,445 - INFO - Processing text with length: 1460 chars
2025-04-05 09:59:20,445 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 09:59:20,446 - INFO - Text length: 1460
2025-04-05 09:59:23,605 - INFO - Torch threads set to 1
2025-04-05 09:59:29,138 - INFO - Loading model...
2025-04-05 09:59:29,139 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 09:59:34,026 - INFO - Model loaded successfully
2025-04-05 09:59:34,026 - INFO - Generating embedding...
2025-04-05 09:59:34,228 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 09:59:34,229 - INFO - Returning successful result
2025-04-05 09:59:34,230 - INFO - Writing result to stdout
2025-04-05 09:59:34,230 - INFO - Result written successfully
2025-04-05 10:00:47,593 - INFO - Starting embedding helper script
2025-04-05 10:00:47,593 - INFO - Reading input from stdin
2025-04-05 10:00:47,594 - INFO - Input data length: 2054
2025-04-05 10:00:47,594 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1908
2025-04-05 10:00:47,594 - INFO - Processing text with length: 1908 chars
2025-04-05 10:00:47,595 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:00:47,595 - INFO - Text length: 1908
2025-04-05 10:00:49,612 - INFO - Torch threads set to 1
2025-04-05 10:00:55,005 - INFO - Loading model...
2025-04-05 10:00:55,006 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:00:59,653 - INFO - Model loaded successfully
2025-04-05 10:00:59,654 - INFO - Generating embedding...
2025-04-05 10:00:59,833 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:00:59,834 - INFO - Returning successful result
2025-04-05 10:00:59,834 - INFO - Writing result to stdout
2025-04-05 10:00:59,835 - INFO - Result written successfully
2025-04-05 10:01:01,012 - INFO - Starting embedding helper script
2025-04-05 10:01:01,013 - INFO - Reading input from stdin
2025-04-05 10:01:01,013 - INFO - Input data length: 2092
2025-04-05 10:01:01,014 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1901
2025-04-05 10:01:01,014 - INFO - Processing text with length: 1901 chars
2025-04-05 10:01:01,015 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:01:01,015 - INFO - Text length: 1901
2025-04-05 10:01:03,565 - INFO - Torch threads set to 1
2025-04-05 10:01:08,109 - INFO - Loading model...
2025-04-05 10:01:08,110 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:01:13,099 - INFO - Model loaded successfully
2025-04-05 10:01:13,099 - INFO - Generating embedding...
2025-04-05 10:01:13,269 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:01:13,270 - INFO - Returning successful result
2025-04-05 10:01:13,270 - INFO - Writing result to stdout
2025-04-05 10:01:13,271 - INFO - Result written successfully
2025-04-05 10:01:14,460 - INFO - Starting embedding helper script
2025-04-05 10:01:14,467 - INFO - Reading input from stdin
2025-04-05 10:01:14,468 - INFO - Input data length: 2013
2025-04-05 10:01:14,468 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1966
2025-04-05 10:01:14,469 - INFO - Processing text with length: 1966 chars
2025-04-05 10:01:14,469 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:01:14,470 - INFO - Text length: 1966
2025-04-05 10:01:16,827 - INFO - Torch threads set to 1
2025-04-05 10:01:21,481 - INFO - Loading model...
2025-04-05 10:01:21,482 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:01:27,841 - INFO - Model loaded successfully
2025-04-05 10:01:27,841 - INFO - Generating embedding...
2025-04-05 10:01:28,021 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:01:28,022 - INFO - Returning successful result
2025-04-05 10:01:28,022 - INFO - Writing result to stdout
2025-04-05 10:01:28,022 - INFO - Result written successfully
2025-04-05 10:01:29,222 - INFO - Starting embedding helper script
2025-04-05 10:01:29,232 - INFO - Reading input from stdin
2025-04-05 10:01:29,235 - INFO - Input data length: 2013
2025-04-05 10:01:29,239 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1967
2025-04-05 10:01:29,242 - INFO - Processing text with length: 1967 chars
2025-04-05 10:01:29,246 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:01:29,249 - INFO - Text length: 1967
2025-04-05 10:01:31,579 - INFO - Torch threads set to 1
2025-04-05 10:01:35,946 - INFO - Loading model...
2025-04-05 10:01:35,947 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:01:41,611 - INFO - Model loaded successfully
2025-04-05 10:01:41,611 - INFO - Generating embedding...
2025-04-05 10:01:41,761 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:01:41,762 - INFO - Returning successful result
2025-04-05 10:01:41,762 - INFO - Writing result to stdout
2025-04-05 10:01:41,763 - INFO - Result written successfully
2025-04-05 10:01:42,942 - INFO - Starting embedding helper script
2025-04-05 10:01:42,943 - INFO - Reading input from stdin
2025-04-05 10:01:42,943 - INFO - Input data length: 2027
2025-04-05 10:01:42,944 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1981
2025-04-05 10:01:42,944 - INFO - Processing text with length: 1981 chars
2025-04-05 10:01:42,944 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:01:42,945 - INFO - Text length: 1981
2025-04-05 10:01:45,365 - INFO - Torch threads set to 1
2025-04-05 10:01:49,785 - INFO - Loading model...
2025-04-05 10:01:49,787 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:01:54,823 - INFO - Model loaded successfully
2025-04-05 10:01:54,823 - INFO - Generating embedding...
2025-04-05 10:01:54,984 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:01:54,985 - INFO - Returning successful result
2025-04-05 10:01:54,985 - INFO - Writing result to stdout
2025-04-05 10:01:54,986 - INFO - Result written successfully
2025-04-05 10:01:56,119 - INFO - Starting embedding helper script
2025-04-05 10:01:56,120 - INFO - Reading input from stdin
2025-04-05 10:01:56,120 - INFO - Input data length: 2001
2025-04-05 10:01:56,120 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1955
2025-04-05 10:01:56,121 - INFO - Processing text with length: 1955 chars
2025-04-05 10:01:56,121 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:01:56,121 - INFO - Text length: 1955
2025-04-05 10:01:58,172 - INFO - Torch threads set to 1
2025-04-05 10:02:03,246 - INFO - Loading model...
2025-04-05 10:02:03,247 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:02:08,907 - INFO - Model loaded successfully
2025-04-05 10:02:08,908 - INFO - Generating embedding...
2025-04-05 10:02:09,059 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:02:09,060 - INFO - Returning successful result
2025-04-05 10:02:09,060 - INFO - Writing result to stdout
2025-04-05 10:02:09,060 - INFO - Result written successfully
2025-04-05 10:02:10,271 - INFO - Starting embedding helper script
2025-04-05 10:02:10,271 - INFO - Reading input from stdin
2025-04-05 10:02:10,272 - INFO - Input data length: 2044
2025-04-05 10:02:10,272 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1998
2025-04-05 10:02:10,272 - INFO - Processing text with length: 1998 chars
2025-04-05 10:02:10,272 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:02:10,273 - INFO - Text length: 1998
2025-04-05 10:02:12,899 - INFO - Torch threads set to 1
2025-04-05 10:02:17,375 - INFO - Loading model...
2025-04-05 10:02:17,376 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:02:22,681 - INFO - Model loaded successfully
2025-04-05 10:02:22,682 - INFO - Generating embedding...
2025-04-05 10:02:22,826 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:02:22,827 - INFO - Returning successful result
2025-04-05 10:02:22,827 - INFO - Writing result to stdout
2025-04-05 10:02:22,827 - INFO - Result written successfully
2025-04-05 10:02:23,966 - INFO - Starting embedding helper script
2025-04-05 10:02:23,966 - INFO - Reading input from stdin
2025-04-05 10:02:23,966 - INFO - Input data length: 2005
2025-04-05 10:02:23,967 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1959
2025-04-05 10:02:23,967 - INFO - Processing text with length: 1959 chars
2025-04-05 10:02:23,967 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:02:23,968 - INFO - Text length: 1959
2025-04-05 10:02:26,478 - INFO - Torch threads set to 1
2025-04-05 10:02:30,890 - INFO - Loading model...
2025-04-05 10:02:30,891 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:02:36,026 - INFO - Model loaded successfully
2025-04-05 10:02:36,026 - INFO - Generating embedding...
2025-04-05 10:02:36,196 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:02:36,196 - INFO - Returning successful result
2025-04-05 10:02:36,197 - INFO - Writing result to stdout
2025-04-05 10:02:36,197 - INFO - Result written successfully
2025-04-05 10:02:37,363 - INFO - Starting embedding helper script
2025-04-05 10:02:37,363 - INFO - Reading input from stdin
2025-04-05 10:02:37,364 - INFO - Input data length: 2004
2025-04-05 10:02:37,364 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1958
2025-04-05 10:02:37,364 - INFO - Processing text with length: 1958 chars
2025-04-05 10:02:37,365 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:02:37,365 - INFO - Text length: 1958
2025-04-05 10:02:40,455 - INFO - Torch threads set to 1
2025-04-05 10:02:45,168 - INFO - Loading model...
2025-04-05 10:02:45,169 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:02:51,099 - INFO - Model loaded successfully
2025-04-05 10:02:51,100 - INFO - Generating embedding...
2025-04-05 10:02:51,263 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:02:51,265 - INFO - Returning successful result
2025-04-05 10:02:51,265 - INFO - Writing result to stdout
2025-04-05 10:02:51,265 - INFO - Result written successfully
2025-04-05 10:02:52,471 - INFO - Starting embedding helper script
2025-04-05 10:02:52,471 - INFO - Reading input from stdin
2025-04-05 10:02:52,472 - INFO - Input data length: 1986
2025-04-05 10:02:52,472 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1940
2025-04-05 10:02:52,472 - INFO - Processing text with length: 1940 chars
2025-04-05 10:02:52,473 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:02:52,473 - INFO - Text length: 1940
2025-04-05 10:02:54,589 - INFO - Torch threads set to 1
2025-04-05 10:03:01,234 - INFO - Loading model...
2025-04-05 10:03:01,235 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:03:08,406 - INFO - Model loaded successfully
2025-04-05 10:03:08,406 - INFO - Generating embedding...
2025-04-05 10:03:08,573 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:03:08,574 - INFO - Returning successful result
2025-04-05 10:03:08,574 - INFO - Writing result to stdout
2025-04-05 10:03:08,575 - INFO - Result written successfully
2025-04-05 10:03:09,911 - INFO - Starting embedding helper script
2025-04-05 10:03:09,911 - INFO - Reading input from stdin
2025-04-05 10:03:09,911 - INFO - Input data length: 2041
2025-04-05 10:03:09,912 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1995
2025-04-05 10:03:09,912 - INFO - Processing text with length: 1995 chars
2025-04-05 10:03:09,912 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:03:09,913 - INFO - Text length: 1995
2025-04-05 10:03:12,156 - INFO - Torch threads set to 1
2025-04-05 10:03:16,887 - INFO - Loading model...
2025-04-05 10:03:16,888 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:03:22,383 - INFO - Model loaded successfully
2025-04-05 10:03:22,384 - INFO - Generating embedding...
2025-04-05 10:03:22,541 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:03:22,542 - INFO - Returning successful result
2025-04-05 10:03:22,542 - INFO - Writing result to stdout
2025-04-05 10:03:22,542 - INFO - Result written successfully
2025-04-05 10:03:23,921 - INFO - Starting embedding helper script
2025-04-05 10:03:23,921 - INFO - Reading input from stdin
2025-04-05 10:03:23,922 - INFO - Input data length: 1981
2025-04-05 10:03:23,922 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1935
2025-04-05 10:03:23,922 - INFO - Processing text with length: 1935 chars
2025-04-05 10:03:23,923 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:03:23,923 - INFO - Text length: 1935
2025-04-05 10:03:27,164 - INFO - Torch threads set to 1
2025-04-05 10:03:32,365 - INFO - Loading model...
2025-04-05 10:03:32,366 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:03:37,378 - INFO - Model loaded successfully
2025-04-05 10:03:37,379 - INFO - Generating embedding...
2025-04-05 10:03:37,538 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:03:37,539 - INFO - Returning successful result
2025-04-05 10:03:37,539 - INFO - Writing result to stdout
2025-04-05 10:03:37,540 - INFO - Result written successfully
2025-04-05 10:03:38,837 - INFO - Starting embedding helper script
2025-04-05 10:03:38,838 - INFO - Reading input from stdin
2025-04-05 10:03:38,838 - INFO - Input data length: 2044
2025-04-05 10:03:38,838 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1998
2025-04-05 10:03:38,839 - INFO - Processing text with length: 1998 chars
2025-04-05 10:03:38,840 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:03:38,840 - INFO - Text length: 1998
2025-04-05 10:03:41,169 - INFO - Torch threads set to 1
2025-04-05 10:03:46,398 - INFO - Loading model...
2025-04-05 10:03:46,399 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:03:51,313 - INFO - Model loaded successfully
2025-04-05 10:03:51,313 - INFO - Generating embedding...
2025-04-05 10:03:51,520 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:03:51,521 - INFO - Returning successful result
2025-04-05 10:03:51,522 - INFO - Writing result to stdout
2025-04-05 10:03:51,522 - INFO - Result written successfully
2025-04-05 10:03:52,814 - INFO - Starting embedding helper script
2025-04-05 10:03:52,814 - INFO - Reading input from stdin
2025-04-05 10:03:52,815 - INFO - Input data length: 1965
2025-04-05 10:03:52,815 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1919
2025-04-05 10:03:52,815 - INFO - Processing text with length: 1919 chars
2025-04-05 10:03:52,816 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:03:52,816 - INFO - Text length: 1919
2025-04-05 10:03:55,649 - INFO - Torch threads set to 1
2025-04-05 10:04:00,356 - INFO - Loading model...
2025-04-05 10:04:00,357 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:04:05,707 - INFO - Model loaded successfully
2025-04-05 10:04:05,708 - INFO - Generating embedding...
2025-04-05 10:04:05,890 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:04:05,891 - INFO - Returning successful result
2025-04-05 10:04:05,891 - INFO - Writing result to stdout
2025-04-05 10:04:05,891 - INFO - Result written successfully
2025-04-05 10:04:07,139 - INFO - Starting embedding helper script
2025-04-05 10:04:07,140 - INFO - Reading input from stdin
2025-04-05 10:04:07,141 - INFO - Input data length: 2040
2025-04-05 10:04:07,141 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1994
2025-04-05 10:04:07,141 - INFO - Processing text with length: 1994 chars
2025-04-05 10:04:07,142 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:04:07,142 - INFO - Text length: 1994
2025-04-05 10:04:09,318 - INFO - Torch threads set to 1
2025-04-05 10:04:14,007 - INFO - Loading model...
2025-04-05 10:04:14,012 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:04:19,571 - INFO - Model loaded successfully
2025-04-05 10:04:19,571 - INFO - Generating embedding...
2025-04-05 10:04:19,738 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:04:19,739 - INFO - Returning successful result
2025-04-05 10:04:19,739 - INFO - Writing result to stdout
2025-04-05 10:04:19,740 - INFO - Result written successfully
2025-04-05 10:04:21,356 - INFO - Starting embedding helper script
2025-04-05 10:04:21,357 - INFO - Reading input from stdin
2025-04-05 10:04:21,357 - INFO - Input data length: 2044
2025-04-05 10:04:21,358 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1998
2025-04-05 10:04:21,358 - INFO - Processing text with length: 1998 chars
2025-04-05 10:04:21,358 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:04:21,359 - INFO - Text length: 1998
2025-04-05 10:04:23,624 - INFO - Torch threads set to 1
2025-04-05 10:04:30,583 - INFO - Loading model...
2025-04-05 10:04:30,585 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:04:35,787 - INFO - Model loaded successfully
2025-04-05 10:04:35,787 - INFO - Generating embedding...
2025-04-05 10:04:35,942 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:04:35,943 - INFO - Returning successful result
2025-04-05 10:04:35,943 - INFO - Writing result to stdout
2025-04-05 10:04:35,944 - INFO - Result written successfully
2025-04-05 10:04:37,352 - INFO - Starting embedding helper script
2025-04-05 10:04:37,367 - INFO - Reading input from stdin
2025-04-05 10:04:37,368 - INFO - Input data length: 2006
2025-04-05 10:04:37,368 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1935
2025-04-05 10:04:37,369 - INFO - Processing text with length: 1935 chars
2025-04-05 10:04:37,369 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:04:37,369 - INFO - Text length: 1935
2025-04-05 10:04:39,841 - INFO - Torch threads set to 1
2025-04-05 10:04:44,964 - INFO - Loading model...
2025-04-05 10:04:44,965 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:04:50,382 - INFO - Model loaded successfully
2025-04-05 10:04:50,382 - INFO - Generating embedding...
2025-04-05 10:04:50,542 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:04:50,543 - INFO - Returning successful result
2025-04-05 10:04:50,543 - INFO - Writing result to stdout
2025-04-05 10:04:50,544 - INFO - Result written successfully
2025-04-05 10:04:51,694 - INFO - Starting embedding helper script
2025-04-05 10:04:51,695 - INFO - Reading input from stdin
2025-04-05 10:04:51,695 - INFO - Input data length: 1893
2025-04-05 10:04:51,695 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1847
2025-04-05 10:04:51,695 - INFO - Processing text with length: 1847 chars
2025-04-05 10:04:51,696 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:04:51,696 - INFO - Text length: 1847
2025-04-05 10:04:53,718 - INFO - Torch threads set to 1
2025-04-05 10:04:58,291 - INFO - Loading model...
2025-04-05 10:04:58,292 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:05:03,927 - INFO - Model loaded successfully
2025-04-05 10:05:03,927 - INFO - Generating embedding...
2025-04-05 10:05:04,079 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:05:04,080 - INFO - Returning successful result
2025-04-05 10:05:04,080 - INFO - Writing result to stdout
2025-04-05 10:05:04,081 - INFO - Result written successfully
2025-04-05 10:05:05,256 - INFO - Starting embedding helper script
2025-04-05 10:05:05,256 - INFO - Reading input from stdin
2025-04-05 10:05:05,257 - INFO - Input data length: 2042
2025-04-05 10:05:05,257 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1991
2025-04-05 10:05:05,257 - INFO - Processing text with length: 1991 chars
2025-04-05 10:05:05,258 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:05:05,258 - INFO - Text length: 1991
2025-04-05 10:05:07,365 - INFO - Torch threads set to 1
2025-04-05 10:05:12,144 - INFO - Loading model...
2025-04-05 10:05:12,146 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:05:17,274 - INFO - Model loaded successfully
2025-04-05 10:05:17,274 - INFO - Generating embedding...
2025-04-05 10:05:17,455 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:05:17,456 - INFO - Returning successful result
2025-04-05 10:05:17,456 - INFO - Writing result to stdout
2025-04-05 10:05:17,457 - INFO - Result written successfully
2025-04-05 10:05:19,295 - INFO - Starting embedding helper script
2025-04-05 10:05:19,295 - INFO - Reading input from stdin
2025-04-05 10:05:19,295 - INFO - Input data length: 1206
2025-04-05 10:05:19,296 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1145
2025-04-05 10:05:19,296 - INFO - Processing text with length: 1145 chars
2025-04-05 10:05:19,296 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:05:19,296 - INFO - Text length: 1145
2025-04-05 10:05:21,445 - INFO - Torch threads set to 1
2025-04-05 10:05:26,314 - INFO - Loading model...
2025-04-05 10:05:26,315 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:05:31,658 - INFO - Model loaded successfully
2025-04-05 10:05:31,658 - INFO - Generating embedding...
2025-04-05 10:05:31,818 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:05:31,819 - INFO - Returning successful result
2025-04-05 10:05:31,820 - INFO - Writing result to stdout
2025-04-05 10:05:31,820 - INFO - Result written successfully
2025-04-05 10:28:15,201 - INFO - Starting embedding helper script
2025-04-05 10:28:15,202 - INFO - Reading input from stdin
2025-04-05 10:28:15,202 - INFO - Input data length: 1565
2025-04-05 10:28:15,202 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1519
2025-04-05 10:28:15,202 - INFO - Processing text with length: 1519 chars
2025-04-05 10:28:15,203 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-05 10:28:15,203 - INFO - Text length: 1519
2025-04-05 10:28:17,063 - INFO - Torch threads set to 1
2025-04-05 10:28:20,974 - INFO - Loading model...
2025-04-05 10:28:20,975 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-05 10:28:26,514 - INFO - Model loaded successfully
2025-04-05 10:28:26,514 - INFO - Generating embedding...
2025-04-05 10:28:26,633 - INFO - Embedding generated successfully with shape: (384,)
2025-04-05 10:28:26,634 - INFO - Returning successful result
2025-04-05 10:28:26,634 - INFO - Writing result to stdout
2025-04-05 10:28:26,634 - INFO - Result written successfully
2025-04-06 06:05:41,362 - INFO - Starting embedding helper script
2025-04-06 06:05:41,363 - INFO - Reading input from stdin
2025-04-06 06:05:41,364 - INFO - Input data length: 9473
2025-04-06 06:05:41,364 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1922
2025-04-06 06:05:41,364 - INFO - Processing text with length: 1922 chars
2025-04-06 06:05:41,365 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 06:05:41,365 - INFO - Text length: 1922
2025-04-06 06:05:44,501 - INFO - Torch threads set to 1
2025-04-06 06:05:50,388 - INFO - Loading model...
2025-04-06 06:05:50,390 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 06:05:55,978 - INFO - Model loaded successfully
2025-04-06 06:05:55,979 - INFO - Generating embedding...
2025-04-06 06:05:56,319 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 06:05:56,321 - INFO - Returning successful result
2025-04-06 06:05:56,321 - INFO - Writing result to stdout
2025-04-06 06:05:56,322 - INFO - Result written successfully
2025-04-06 06:05:57,521 - INFO - Starting embedding helper script
2025-04-06 06:05:57,522 - INFO - Reading input from stdin
2025-04-06 06:05:57,522 - INFO - Input data length: 6088
2025-04-06 06:05:57,522 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1222
2025-04-06 06:05:57,523 - INFO - Processing text with length: 1222 chars
2025-04-06 06:05:57,523 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 06:05:57,523 - INFO - Text length: 1222
2025-04-06 06:05:59,963 - INFO - Torch threads set to 1
2025-04-06 06:06:04,677 - INFO - Loading model...
2025-04-06 06:06:04,678 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 06:06:09,384 - INFO - Model loaded successfully
2025-04-06 06:06:09,384 - INFO - Generating embedding...
2025-04-06 06:06:09,522 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 06:06:09,523 - INFO - Returning successful result
2025-04-06 06:06:09,524 - INFO - Writing result to stdout
2025-04-06 06:06:09,524 - INFO - Result written successfully
2025-04-06 06:06:10,727 - INFO - Starting embedding helper script
2025-04-06 06:06:10,728 - INFO - Reading input from stdin
2025-04-06 06:06:10,728 - INFO - Input data length: 9633
2025-04-06 06:06:10,728 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1967
2025-04-06 06:06:10,729 - INFO - Processing text with length: 1967 chars
2025-04-06 06:06:10,729 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 06:06:10,729 - INFO - Text length: 1967
2025-04-06 06:06:12,932 - INFO - Torch threads set to 1
2025-04-06 06:06:17,354 - INFO - Loading model...
2025-04-06 06:06:17,355 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 06:06:22,341 - INFO - Model loaded successfully
2025-04-06 06:06:22,341 - INFO - Generating embedding...
2025-04-06 06:06:22,480 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 06:06:22,481 - INFO - Returning successful result
2025-04-06 06:06:22,481 - INFO - Writing result to stdout
2025-04-06 06:06:22,482 - INFO - Result written successfully
2025-04-06 06:06:23,591 - INFO - Starting embedding helper script
2025-04-06 06:06:23,592 - INFO - Reading input from stdin
2025-04-06 06:06:23,592 - INFO - Input data length: 9726
2025-04-06 06:06:23,593 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1985
2025-04-06 06:06:23,593 - INFO - Processing text with length: 1985 chars
2025-04-06 06:06:23,594 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 06:06:23,594 - INFO - Text length: 1985
2025-04-06 06:06:25,826 - INFO - Torch threads set to 1
2025-04-06 06:06:30,637 - INFO - Loading model...
2025-04-06 06:06:30,638 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 06:06:35,896 - INFO - Model loaded successfully
2025-04-06 06:06:35,896 - INFO - Generating embedding...
2025-04-06 06:06:36,045 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 06:06:36,046 - INFO - Returning successful result
2025-04-06 06:06:36,046 - INFO - Writing result to stdout
2025-04-06 06:06:36,047 - INFO - Result written successfully
2025-04-06 06:06:37,274 - INFO - Starting embedding helper script
2025-04-06 06:06:37,275 - INFO - Reading input from stdin
2025-04-06 06:06:37,275 - INFO - Input data length: 9826
2025-04-06 06:06:37,276 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1980
2025-04-06 06:06:37,276 - INFO - Processing text with length: 1980 chars
2025-04-06 06:06:37,276 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 06:06:37,277 - INFO - Text length: 1980
2025-04-06 06:06:39,811 - INFO - Torch threads set to 1
2025-04-06 06:06:44,916 - INFO - Loading model...
2025-04-06 06:06:44,918 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 06:06:50,106 - INFO - Model loaded successfully
2025-04-06 06:06:50,109 - INFO - Generating embedding...
2025-04-06 06:06:50,402 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 06:06:50,404 - INFO - Returning successful result
2025-04-06 06:06:50,404 - INFO - Writing result to stdout
2025-04-06 06:06:50,405 - INFO - Result written successfully
2025-04-06 06:06:51,885 - INFO - Starting embedding helper script
2025-04-06 06:06:51,886 - INFO - Reading input from stdin
2025-04-06 06:06:51,886 - INFO - Input data length: 1947
2025-04-06 06:06:51,886 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=371
2025-04-06 06:06:51,887 - INFO - Processing text with length: 371 chars
2025-04-06 06:06:51,887 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 06:06:51,887 - INFO - Text length: 371
2025-04-06 06:06:54,199 - INFO - Torch threads set to 1
2025-04-06 06:06:58,758 - INFO - Loading model...
2025-04-06 06:06:58,759 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 06:07:03,865 - INFO - Model loaded successfully
2025-04-06 06:07:03,865 - INFO - Generating embedding...
2025-04-06 06:07:03,912 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 06:07:03,913 - INFO - Returning successful result
2025-04-06 06:07:03,913 - INFO - Writing result to stdout
2025-04-06 06:07:03,913 - INFO - Result written successfully
2025-04-06 10:15:05,659 - INFO - Starting embedding helper script
2025-04-06 10:15:05,661 - INFO - Reading input from stdin
2025-04-06 10:15:05,662 - INFO - Input data length: 9090
2025-04-06 10:15:05,663 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1884
2025-04-06 10:15:05,663 - INFO - Processing text with length: 1884 chars
2025-04-06 10:15:05,664 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:15:05,664 - INFO - Text length: 1884
2025-04-06 10:15:08,484 - INFO - Torch threads set to 1
2025-04-06 10:15:14,172 - INFO - Loading model...
2025-04-06 10:15:14,173 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:15:29,562 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:15:29,563 - INFO - Writing result to stdout
2025-04-06 10:15:29,563 - INFO - Result written successfully
2025-04-06 10:15:30,777 - INFO - Starting embedding helper script
2025-04-06 10:15:30,777 - INFO - Reading input from stdin
2025-04-06 10:15:30,778 - INFO - Input data length: 9477
2025-04-06 10:15:30,778 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1966
2025-04-06 10:15:30,778 - INFO - Processing text with length: 1966 chars
2025-04-06 10:15:30,779 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:15:30,779 - INFO - Text length: 1966
2025-04-06 10:15:32,969 - INFO - Torch threads set to 1
2025-04-06 10:15:37,508 - INFO - Loading model...
2025-04-06 10:15:37,509 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:15:41,692 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:15:41,693 - INFO - Writing result to stdout
2025-04-06 10:15:41,693 - INFO - Result written successfully
2025-04-06 10:15:42,870 - INFO - Starting embedding helper script
2025-04-06 10:15:42,871 - INFO - Reading input from stdin
2025-04-06 10:15:42,871 - INFO - Input data length: 8516
2025-04-06 10:15:42,871 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1695
2025-04-06 10:15:42,872 - INFO - Processing text with length: 1695 chars
2025-04-06 10:15:42,872 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:15:42,872 - INFO - Text length: 1695
2025-04-06 10:15:45,179 - INFO - Torch threads set to 1
2025-04-06 10:15:49,702 - INFO - Loading model...
2025-04-06 10:15:49,703 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:15:53,161 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:15:53,162 - INFO - Writing result to stdout
2025-04-06 10:15:53,162 - INFO - Result written successfully
2025-04-06 10:15:54,407 - INFO - Starting embedding helper script
2025-04-06 10:15:54,408 - INFO - Reading input from stdin
2025-04-06 10:15:54,408 - INFO - Input data length: 9589
2025-04-06 10:15:54,408 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1963
2025-04-06 10:15:54,409 - INFO - Processing text with length: 1963 chars
2025-04-06 10:15:54,409 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:15:54,409 - INFO - Text length: 1963
2025-04-06 10:15:56,715 - INFO - Torch threads set to 1
2025-04-06 10:16:01,519 - INFO - Loading model...
2025-04-06 10:16:01,520 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:16:05,285 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:16:05,286 - INFO - Writing result to stdout
2025-04-06 10:16:05,286 - INFO - Result written successfully
2025-04-06 10:16:06,495 - INFO - Starting embedding helper script
2025-04-06 10:16:06,496 - INFO - Reading input from stdin
2025-04-06 10:16:06,496 - INFO - Input data length: 9573
2025-04-06 10:16:06,497 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1967
2025-04-06 10:16:06,497 - INFO - Processing text with length: 1967 chars
2025-04-06 10:16:06,498 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:16:06,498 - INFO - Text length: 1967
2025-04-06 10:16:08,847 - INFO - Torch threads set to 1
2025-04-06 10:16:13,295 - INFO - Loading model...
2025-04-06 10:16:13,296 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:16:17,041 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:16:17,041 - INFO - Writing result to stdout
2025-04-06 10:16:17,042 - INFO - Result written successfully
2025-04-06 10:16:18,192 - INFO - Starting embedding helper script
2025-04-06 10:16:18,192 - INFO - Reading input from stdin
2025-04-06 10:16:18,193 - INFO - Input data length: 9313
2025-04-06 10:16:18,193 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1902
2025-04-06 10:16:18,193 - INFO - Processing text with length: 1902 chars
2025-04-06 10:16:18,194 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:16:18,194 - INFO - Text length: 1902
2025-04-06 10:16:20,347 - INFO - Torch threads set to 1
2025-04-06 10:16:24,880 - INFO - Loading model...
2025-04-06 10:16:24,881 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:16:28,239 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:16:28,239 - INFO - Writing result to stdout
2025-04-06 10:16:28,240 - INFO - Result written successfully
2025-04-06 10:16:29,484 - INFO - Starting embedding helper script
2025-04-06 10:16:29,484 - INFO - Reading input from stdin
2025-04-06 10:16:29,484 - INFO - Input data length: 9129
2025-04-06 10:16:29,485 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1853
2025-04-06 10:16:29,485 - INFO - Processing text with length: 1853 chars
2025-04-06 10:16:29,485 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:16:29,486 - INFO - Text length: 1853
2025-04-06 10:16:31,960 - INFO - Torch threads set to 1
2025-04-06 10:16:36,467 - INFO - Loading model...
2025-04-06 10:16:36,470 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:16:40,045 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:16:40,046 - INFO - Writing result to stdout
2025-04-06 10:16:40,046 - INFO - Result written successfully
2025-04-06 10:16:41,241 - INFO - Starting embedding helper script
2025-04-06 10:16:41,241 - INFO - Reading input from stdin
2025-04-06 10:16:41,241 - INFO - Input data length: 4649
2025-04-06 10:16:41,242 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=918
2025-04-06 10:16:41,242 - INFO - Processing text with length: 918 chars
2025-04-06 10:16:41,242 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:16:41,243 - INFO - Text length: 918
2025-04-06 10:16:43,404 - INFO - Torch threads set to 1
2025-04-06 10:16:47,946 - INFO - Loading model...
2025-04-06 10:16:47,947 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:16:51,441 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:16:51,442 - INFO - Writing result to stdout
2025-04-06 10:16:51,442 - INFO - Result written successfully
2025-04-06 10:16:52,810 - INFO - Starting embedding helper script
2025-04-06 10:16:52,810 - INFO - Reading input from stdin
2025-04-06 10:16:52,810 - INFO - Input data length: 9487
2025-04-06 10:16:52,811 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1991
2025-04-06 10:16:52,811 - INFO - Processing text with length: 1991 chars
2025-04-06 10:16:52,811 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:16:52,812 - INFO - Text length: 1991
2025-04-06 10:16:55,337 - INFO - Torch threads set to 1
2025-04-06 10:17:00,668 - INFO - Loading model...
2025-04-06 10:17:00,670 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:17:04,413 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:17:04,414 - INFO - Writing result to stdout
2025-04-06 10:17:04,414 - INFO - Result written successfully
2025-04-06 10:17:05,844 - INFO - Starting embedding helper script
2025-04-06 10:17:05,844 - INFO - Reading input from stdin
2025-04-06 10:17:05,845 - INFO - Input data length: 9582
2025-04-06 10:17:05,845 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1976
2025-04-06 10:17:05,846 - INFO - Processing text with length: 1976 chars
2025-04-06 10:17:05,846 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:17:05,846 - INFO - Text length: 1976
2025-04-06 10:17:08,425 - INFO - Torch threads set to 1
2025-04-06 10:17:15,421 - INFO - Loading model...
2025-04-06 10:17:15,422 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:17:19,555 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:17:19,556 - INFO - Writing result to stdout
2025-04-06 10:17:19,557 - INFO - Result written successfully
2025-04-06 10:17:20,930 - INFO - Starting embedding helper script
2025-04-06 10:17:20,931 - INFO - Reading input from stdin
2025-04-06 10:17:20,931 - INFO - Input data length: 8502
2025-04-06 10:17:20,932 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1731
2025-04-06 10:17:20,932 - INFO - Processing text with length: 1731 chars
2025-04-06 10:17:20,932 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:17:20,932 - INFO - Text length: 1731
2025-04-06 10:17:24,058 - INFO - Torch threads set to 1
2025-04-06 10:17:28,639 - INFO - Loading model...
2025-04-06 10:17:28,640 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:17:32,632 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:17:32,632 - INFO - Writing result to stdout
2025-04-06 10:17:32,633 - INFO - Result written successfully
2025-04-06 10:17:33,925 - INFO - Starting embedding helper script
2025-04-06 10:17:33,927 - INFO - Reading input from stdin
2025-04-06 10:17:33,927 - INFO - Input data length: 9012
2025-04-06 10:17:33,928 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1886
2025-04-06 10:17:33,928 - INFO - Processing text with length: 1886 chars
2025-04-06 10:17:33,929 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:17:33,929 - INFO - Text length: 1886
2025-04-06 10:17:37,382 - INFO - Torch threads set to 1
2025-04-06 10:17:42,586 - INFO - Loading model...
2025-04-06 10:17:42,587 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:17:47,081 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:17:47,081 - INFO - Writing result to stdout
2025-04-06 10:17:47,082 - INFO - Result written successfully
2025-04-06 10:17:48,629 - INFO - Starting embedding helper script
2025-04-06 10:17:48,630 - INFO - Reading input from stdin
2025-04-06 10:17:48,630 - INFO - Input data length: 7035
2025-04-06 10:17:48,631 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1434
2025-04-06 10:17:48,631 - INFO - Processing text with length: 1434 chars
2025-04-06 10:17:48,631 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:17:48,632 - INFO - Text length: 1434
2025-04-06 10:17:51,362 - INFO - Torch threads set to 1
2025-04-06 10:17:58,167 - INFO - Loading model...
2025-04-06 10:17:58,169 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:18:02,073 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 44, in generate_embedding
    try:
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:18:02,073 - INFO - Writing result to stdout
2025-04-06 10:18:02,074 - INFO - Result written successfully
2025-04-06 10:18:03,493 - INFO - Starting embedding helper script
2025-04-06 10:18:03,493 - INFO - Reading input from stdin
2025-04-06 10:18:03,494 - INFO - Input data length: 9090
2025-04-06 10:18:03,494 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1884
2025-04-06 10:18:03,495 - INFO - Processing text with length: 1884 chars
2025-04-06 10:18:03,495 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:18:03,496 - INFO - Text length: 1884
2025-04-06 10:18:05,956 - INFO - Torch threads set to 1
2025-04-06 10:18:11,339 - INFO - Loading model...
2025-04-06 10:18:11,339 - INFO - Using fallback init_empty_weights implementation
2025-04-06 10:18:11,340 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:18:15,155 - WARNING - Standard loading failed: name 'init_empty_weights' is not defined. Trying with additional parameters.
2025-04-06 10:18:15,156 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:18:17,778 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 57, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 61, in generate_embedding
    # Try with different loading parameters
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:18:17,779 - INFO - Writing result to stdout
2025-04-06 10:18:17,779 - INFO - Result written successfully
2025-04-06 10:18:19,219 - INFO - Starting embedding helper script
2025-04-06 10:18:19,220 - INFO - Reading input from stdin
2025-04-06 10:18:19,220 - INFO - Input data length: 9477
2025-04-06 10:18:19,221 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1966
2025-04-06 10:18:19,221 - INFO - Processing text with length: 1966 chars
2025-04-06 10:18:19,222 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:18:19,222 - INFO - Text length: 1966
2025-04-06 10:18:21,786 - INFO - Torch threads set to 1
2025-04-06 10:18:26,764 - INFO - Loading model...
2025-04-06 10:18:26,765 - INFO - Using fallback init_empty_weights implementation
2025-04-06 10:18:26,766 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:18:30,566 - WARNING - Standard loading failed: name 'init_empty_weights' is not defined. Trying with additional parameters.
2025-04-06 10:18:30,567 - WARNING - Alternative loading method 1 failed: cannot import name 'no_init_weights' from 'transformers.utils' (/usr/local/lib/python3.10/dist-packages/transformers/utils/__init__.py). Trying method 2.
2025-04-06 10:18:30,569 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:18:33,164 - ERROR - Error generating embedding: name 'init_empty_weights' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 57, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 64, in generate_embedding
    from transformers.utils import no_init_weights
ImportError: cannot import name 'no_init_weights' from 'transformers.utils' (/usr/local/lib/python3.10/dist-packages/transformers/utils/__init__.py)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 70, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu',
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4333, in from_pretrained
    model_init_context = cls.get_init_context(is_quantized, _is_ds_init_called)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 3736, in get_init_context
    init_contexts = [no_init_weights(), init_empty_weights()]
NameError: name 'init_empty_weights' is not defined

2025-04-06 10:18:33,164 - INFO - Writing result to stdout
2025-04-06 10:18:33,165 - INFO - Result written successfully
2025-04-06 10:18:34,604 - INFO - Starting embedding helper script
2025-04-06 10:18:34,604 - INFO - Reading input from stdin
2025-04-06 10:18:34,604 - INFO - Input data length: 8516
2025-04-06 10:18:34,605 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1695
2025-04-06 10:18:34,605 - INFO - Processing text with length: 1695 chars
2025-04-06 10:18:34,606 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:18:34,606 - INFO - Text length: 1695
2025-04-06 10:18:37,529 - INFO - Torch threads set to 1
2025-04-06 10:18:42,813 - INFO - Loading model...
2025-04-06 10:18:42,814 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:18:42,814 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:18:47,494 - WARNING - Standard loading failed: name 'find_tied_parameters' is not defined. Trying with additional parameters.
2025-04-06 10:18:47,495 - WARNING - Alternative loading method 1 failed: cannot import name 'no_init_weights' from 'transformers.utils' (/usr/local/lib/python3.10/dist-packages/transformers/utils/__init__.py). Trying method 2.
2025-04-06 10:18:47,496 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:18:50,690 - ERROR - Error generating embedding: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 57, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 64, in generate_embedding
    from transformers.utils import no_init_weights
ImportError: cannot import name 'no_init_weights' from 'transformers.utils' (/usr/local/lib/python3.10/dist-packages/transformers/utils/__init__.py)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 70, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu',
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

2025-04-06 10:18:50,696 - INFO - Writing result to stdout
2025-04-06 10:18:50,697 - INFO - Result written successfully
2025-04-06 10:18:53,957 - INFO - Starting embedding helper script
2025-04-06 10:18:53,957 - INFO - Reading input from stdin
2025-04-06 10:18:53,958 - INFO - Input data length: 9773
2025-04-06 10:18:53,958 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1992
2025-04-06 10:18:53,959 - INFO - Processing text with length: 1992 chars
2025-04-06 10:18:53,959 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:18:53,959 - INFO - Text length: 1992
2025-04-06 10:19:00,219 - INFO - Torch threads set to 1
2025-04-06 10:19:08,032 - INFO - Loading model...
2025-04-06 10:19:08,032 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:19:08,033 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:19:11,517 - WARNING - Standard loading failed: name 'find_tied_parameters' is not defined. Trying with additional parameters.
2025-04-06 10:19:11,518 - WARNING - Alternative loading method 1 failed: cannot import name 'no_init_weights' from 'transformers.utils' (/usr/local/lib/python3.10/dist-packages/transformers/utils/__init__.py). Trying method 2.
2025-04-06 10:19:11,519 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:19:14,370 - ERROR - Error generating embedding: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 57, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 64, in generate_embedding
    logging.info("Trying simplified model loading approach")
ImportError: cannot import name 'no_init_weights' from 'transformers.utils' (/usr/local/lib/python3.10/dist-packages/transformers/utils/__init__.py)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 70, in generate_embedding
    logging.info("Attempting to load a different, more compatible model")
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

2025-04-06 10:19:14,372 - INFO - Writing result to stdout
2025-04-06 10:19:14,373 - INFO - Result written successfully
2025-04-06 10:19:15,597 - INFO - Starting embedding helper script
2025-04-06 10:19:15,598 - INFO - Reading input from stdin
2025-04-06 10:19:15,598 - INFO - Input data length: 9512
2025-04-06 10:19:15,598 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1931
2025-04-06 10:19:15,599 - INFO - Processing text with length: 1931 chars
2025-04-06 10:19:15,599 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:19:15,600 - INFO - Text length: 1931
2025-04-06 10:19:18,840 - INFO - Torch threads set to 1
2025-04-06 10:19:24,521 - INFO - Loading model...
2025-04-06 10:19:24,521 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:19:24,522 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:19:28,487 - WARNING - Standard loading failed: name 'find_tied_parameters' is not defined. Trying with additional parameters.
2025-04-06 10:19:28,488 - INFO - Trying simplified model loading approach
2025-04-06 10:19:28,489 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:19:31,374 - WARNING - Alternative loading method failed: name 'find_tied_parameters' is not defined. Using last resort method.
2025-04-06 10:19:31,375 - INFO - Attempting to load a different, more compatible model
2025-04-06 10:19:31,376 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:19:34,231 - ERROR - Error generating embedding: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 57, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 66, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 72, in generate_embedding
    model = SentenceTransformer(fallback_model, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

2025-04-06 10:19:34,234 - INFO - Writing result to stdout
2025-04-06 10:19:34,235 - INFO - Result written successfully
2025-04-06 10:19:35,515 - INFO - Starting embedding helper script
2025-04-06 10:19:35,515 - INFO - Reading input from stdin
2025-04-06 10:19:35,516 - INFO - Input data length: 9900
2025-04-06 10:19:35,516 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1994
2025-04-06 10:19:35,517 - INFO - Processing text with length: 1994 chars
2025-04-06 10:19:35,517 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:19:35,517 - INFO - Text length: 1994
2025-04-06 10:19:38,137 - INFO - Torch threads set to 1
2025-04-06 10:19:43,795 - INFO - Loading model...
2025-04-06 10:19:43,795 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:19:43,797 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:19:47,564 - WARNING - Standard loading failed: name 'find_tied_parameters' is not defined. Trying with additional parameters.
2025-04-06 10:19:47,565 - INFO - Trying simplified model loading approach
2025-04-06 10:19:47,566 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:19:50,218 - WARNING - Alternative loading method failed: name 'find_tied_parameters' is not defined. Using last resort method.
2025-04-06 10:19:50,218 - INFO - Attempting to load a different, more compatible model
2025-04-06 10:19:50,219 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:19:52,942 - ERROR - Error generating embedding: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 57, in generate_embedding
    logging.info("Added fallback init_empty_weights implementation")
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 66, in generate_embedding
    # Try with a more basic approach - load the model with minimal parameters
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 72, in generate_embedding
    # Last resort: Try to use a different model that's more compatible
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

2025-04-06 10:19:52,946 - INFO - Writing result to stdout
2025-04-06 10:19:52,946 - INFO - Result written successfully
2025-04-06 10:19:54,282 - INFO - Starting embedding helper script
2025-04-06 10:19:54,282 - INFO - Reading input from stdin
2025-04-06 10:19:54,282 - INFO - Input data length: 9044
2025-04-06 10:19:54,283 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1943
2025-04-06 10:19:54,283 - INFO - Processing text with length: 1943 chars
2025-04-06 10:19:54,284 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:19:54,284 - INFO - Text length: 1943
2025-04-06 10:19:56,911 - INFO - Torch threads set to 1
2025-04-06 10:20:01,978 - INFO - Loading model...
2025-04-06 10:20:01,979 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:20:01,980 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:20:05,553 - WARNING - Standard loading failed: name 'find_tied_parameters' is not defined. Trying with additional parameters.
2025-04-06 10:20:05,553 - INFO - Trying simplified model loading approach
2025-04-06 10:20:05,554 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:20:08,547 - WARNING - Alternative loading method failed: name 'find_tied_parameters' is not defined. Using last resort method.
2025-04-06 10:20:08,548 - INFO - Attempting to load a different, more compatible model
2025-04-06 10:20:08,549 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:20:11,195 - ERROR - Error generating embedding: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 60, in generate_embedding
    except Exception as patch_error:
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 69, in generate_embedding
    try:
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 75, in generate_embedding
    logging.error(f"All model loading attempts failed: {str(e2)}")
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

2025-04-06 10:20:11,199 - INFO - Writing result to stdout
2025-04-06 10:20:11,199 - INFO - Result written successfully
2025-04-06 10:20:12,527 - INFO - Starting embedding helper script
2025-04-06 10:20:12,527 - INFO - Reading input from stdin
2025-04-06 10:20:12,528 - INFO - Input data length: 6230
2025-04-06 10:20:12,528 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1244
2025-04-06 10:20:12,528 - INFO - Processing text with length: 1244 chars
2025-04-06 10:20:12,529 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:20:12,529 - INFO - Text length: 1244
2025-04-06 10:20:15,173 - INFO - Torch threads set to 1
2025-04-06 10:20:20,574 - INFO - Loading model...
2025-04-06 10:20:20,575 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:20:20,576 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:20:24,161 - WARNING - Standard model loading failed: name 'find_tied_parameters' is not defined
2025-04-06 10:20:24,161 - INFO - Attempting to load a fallback model
2025-04-06 10:20:24,163 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:20:26,770 - ERROR - All model loading attempts failed: name 'find_tied_parameters' is not defined
2025-04-06 10:20:26,775 - ERROR - Error generating embedding: Failed to load any embedding model: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 64, in generate_embedding
    yield
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 72, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 76, in generate_embedding
    # Try with a fallback model
Exception: Failed to load any embedding model: name 'find_tied_parameters' is not defined

2025-04-06 10:20:26,778 - INFO - Writing result to stdout
2025-04-06 10:20:26,779 - INFO - Result written successfully
2025-04-06 10:20:28,222 - INFO - Starting embedding helper script
2025-04-06 10:20:28,222 - INFO - Reading input from stdin
2025-04-06 10:20:28,222 - INFO - Input data length: 9413
2025-04-06 10:20:28,223 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1947
2025-04-06 10:20:28,223 - INFO - Processing text with length: 1947 chars
2025-04-06 10:20:28,224 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:20:28,224 - INFO - Text length: 1947
2025-04-06 10:20:31,085 - INFO - Torch threads set to 1
2025-04-06 10:20:37,250 - INFO - Loading model...
2025-04-06 10:20:37,251 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:20:37,251 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:20:41,374 - WARNING - Standard model loading failed: name 'find_tied_parameters' is not defined
2025-04-06 10:20:41,374 - INFO - Attempting to load a fallback model
2025-04-06 10:20:41,375 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:20:44,443 - ERROR - All model loading attempts failed: name 'find_tied_parameters' is not defined
2025-04-06 10:20:44,450 - ERROR - Error generating embedding: Failed to load any embedding model: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 72, in generate_embedding
    logging.info("Added fallback init_empty_weights implementation")
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 80, in generate_embedding
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 84, in generate_embedding
    fallback_model = 'all-MiniLM-L6-v2'  # A smaller, more compatible model
Exception: Failed to load any embedding model: name 'find_tied_parameters' is not defined

2025-04-06 10:20:44,452 - INFO - Writing result to stdout
2025-04-06 10:20:44,453 - INFO - Result written successfully
2025-04-06 10:20:45,626 - INFO - Starting embedding helper script
2025-04-06 10:20:45,627 - INFO - Reading input from stdin
2025-04-06 10:20:45,627 - INFO - Input data length: 9031
2025-04-06 10:20:45,628 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1840
2025-04-06 10:20:45,628 - INFO - Processing text with length: 1840 chars
2025-04-06 10:20:45,628 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:20:45,628 - INFO - Text length: 1840
2025-04-06 10:20:47,928 - INFO - Torch threads set to 1
2025-04-06 10:20:52,752 - INFO - Loading model...
2025-04-06 10:20:52,752 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:20:52,753 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:20:56,923 - WARNING - Standard model loading failed: name 'find_tied_parameters' is not defined
2025-04-06 10:20:56,923 - INFO - Attempting to load a fallback model
2025-04-06 10:20:56,924 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:20:59,726 - ERROR - All model loading attempts failed: name 'find_tied_parameters' is not defined
2025-04-06 10:20:59,729 - ERROR - Error generating embedding: Failed to load any embedding model: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 77, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 85, in generate_embedding
    model = SentenceTransformer(fallback_model, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 89, in generate_embedding
    raise Exception(f"Failed to load any embedding model: {str(e2)}")
Exception: Failed to load any embedding model: name 'find_tied_parameters' is not defined

2025-04-06 10:20:59,732 - INFO - Writing result to stdout
2025-04-06 10:20:59,732 - INFO - Result written successfully
2025-04-06 10:22:56,289 - INFO - Starting embedding helper script
2025-04-06 10:22:56,290 - INFO - Reading input from stdin
2025-04-06 10:22:56,290 - INFO - Input data length: 9090
2025-04-06 10:22:56,291 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1884
2025-04-06 10:22:56,291 - INFO - Processing text with length: 1884 chars
2025-04-06 10:22:56,291 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:22:56,292 - INFO - Text length: 1884
2025-04-06 10:23:01,286 - INFO - Torch threads set to 1
2025-04-06 10:23:06,174 - INFO - Loading model...
2025-04-06 10:23:06,175 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:23:06,176 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:23:10,244 - WARNING - Standard model loading failed: name 'find_tied_parameters' is not defined
2025-04-06 10:23:10,244 - INFO - Attempting to load a fallback model
2025-04-06 10:23:10,245 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:23:13,024 - ERROR - All model loading attempts failed: name 'find_tied_parameters' is not defined
2025-04-06 10:23:13,028 - ERROR - Error generating embedding: Failed to load any embedding model: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 77, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 85, in generate_embedding
    model = SentenceTransformer(fallback_model, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 89, in generate_embedding
    raise Exception(f"Failed to load any embedding model: {str(e2)}")
Exception: Failed to load any embedding model: name 'find_tied_parameters' is not defined

2025-04-06 10:23:13,030 - INFO - Writing result to stdout
2025-04-06 10:23:13,030 - INFO - Result written successfully
2025-04-06 10:23:14,282 - INFO - Starting embedding helper script
2025-04-06 10:23:14,282 - INFO - Reading input from stdin
2025-04-06 10:23:14,283 - INFO - Input data length: 9477
2025-04-06 10:23:14,283 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1966
2025-04-06 10:23:14,284 - INFO - Processing text with length: 1966 chars
2025-04-06 10:23:14,284 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:23:14,284 - INFO - Text length: 1966
2025-04-06 10:23:16,873 - INFO - Torch threads set to 1
2025-04-06 10:23:23,329 - INFO - Loading model...
2025-04-06 10:23:23,330 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:23:23,331 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:23:27,041 - WARNING - Standard model loading failed: name 'find_tied_parameters' is not defined
2025-04-06 10:23:27,041 - INFO - Attempting to load a fallback model
2025-04-06 10:23:27,042 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:23:29,897 - ERROR - All model loading attempts failed: name 'find_tied_parameters' is not defined
2025-04-06 10:23:29,901 - ERROR - Error generating embedding: Failed to load any embedding model: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 77, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 85, in generate_embedding
    model = SentenceTransformer(fallback_model, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 89, in generate_embedding
    raise Exception(f"Failed to load any embedding model: {str(e2)}")
Exception: Failed to load any embedding model: name 'find_tied_parameters' is not defined

2025-04-06 10:23:29,904 - INFO - Writing result to stdout
2025-04-06 10:23:29,904 - INFO - Result written successfully
2025-04-06 10:23:30,994 - INFO - Starting embedding helper script
2025-04-06 10:23:30,995 - INFO - Reading input from stdin
2025-04-06 10:23:30,995 - INFO - Input data length: 8516
2025-04-06 10:23:30,996 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1695
2025-04-06 10:23:30,996 - INFO - Processing text with length: 1695 chars
2025-04-06 10:23:30,996 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:23:30,997 - INFO - Text length: 1695
2025-04-06 10:23:33,483 - INFO - Torch threads set to 1
2025-04-06 10:23:38,162 - INFO - Loading model...
2025-04-06 10:23:38,163 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:23:38,164 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:23:41,890 - WARNING - Standard model loading failed: name 'find_tied_parameters' is not defined
2025-04-06 10:23:41,891 - INFO - Attempting to load a fallback model
2025-04-06 10:23:41,892 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:23:44,487 - ERROR - All model loading attempts failed: name 'find_tied_parameters' is not defined
2025-04-06 10:23:44,506 - ERROR - Error generating embedding: Failed to load any embedding model: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 77, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 85, in generate_embedding
    model = SentenceTransformer(fallback_model, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 89, in generate_embedding
    raise Exception(f"Failed to load any embedding model: {str(e2)}")
Exception: Failed to load any embedding model: name 'find_tied_parameters' is not defined

2025-04-06 10:23:44,509 - INFO - Writing result to stdout
2025-04-06 10:23:44,510 - INFO - Result written successfully
2025-04-06 10:23:46,028 - INFO - Starting embedding helper script
2025-04-06 10:23:46,029 - INFO - Reading input from stdin
2025-04-06 10:23:46,029 - INFO - Input data length: 9589
2025-04-06 10:23:46,030 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1963
2025-04-06 10:23:46,031 - INFO - Processing text with length: 1963 chars
2025-04-06 10:23:46,031 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:23:46,032 - INFO - Text length: 1963
2025-04-06 10:23:49,389 - INFO - Torch threads set to 1
2025-04-06 10:23:55,940 - INFO - Loading model...
2025-04-06 10:23:55,941 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:23:55,942 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:23:59,711 - WARNING - Standard model loading failed: name 'find_tied_parameters' is not defined
2025-04-06 10:23:59,712 - INFO - Attempting to load a fallback model
2025-04-06 10:23:59,713 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:24:02,565 - ERROR - All model loading attempts failed: name 'find_tied_parameters' is not defined
2025-04-06 10:24:02,569 - ERROR - Error generating embedding: Failed to load any embedding model: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 77, in generate_embedding
    model = SentenceTransformer(model_name, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 85, in generate_embedding
    model = SentenceTransformer(fallback_model, device='cpu')
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 89, in generate_embedding
    raise Exception(f"Failed to load any embedding model: {str(e2)}")
Exception: Failed to load any embedding model: name 'find_tied_parameters' is not defined

2025-04-06 10:24:02,571 - INFO - Writing result to stdout
2025-04-06 10:24:02,572 - INFO - Result written successfully
2025-04-06 10:24:04,169 - INFO - Starting embedding helper script
2025-04-06 10:24:04,169 - INFO - Reading input from stdin
2025-04-06 10:24:04,170 - INFO - Input data length: 9573
2025-04-06 10:24:04,170 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1967
2025-04-06 10:24:04,170 - INFO - Processing text with length: 1967 chars
2025-04-06 10:24:04,171 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:24:04,171 - INFO - Text length: 1967
2025-04-06 10:24:07,855 - INFO - Torch threads set to 1
2025-04-06 10:24:14,298 - INFO - Loading model...
2025-04-06 10:24:14,299 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:24:14,300 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:24:18,035 - WARNING - Standard model loading failed: name 'find_tied_parameters' is not defined
2025-04-06 10:24:18,036 - INFO - Attempting to load a fallback model
2025-04-06 10:24:18,037 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:24:21,267 - ERROR - All model loading attempts failed: name 'find_tied_parameters' is not defined
2025-04-06 10:24:21,319 - ERROR - Error generating embedding: Failed to load any embedding model: name 'find_tied_parameters' is not defined
Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 77, in generate_embedding
    logging.info("Added fallback find_tied_parameters implementation")
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 85, in generate_embedding
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 309, in __init__
    modules, self.module_kwargs = self._load_sbert_model(
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/SentenceTransformer.py", line 1802, in _load_sbert_model
    module = module_class(model_name_or_path, cache_dir=cache_folder, backend=self.backend, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 81, in __init__
    self._load_model(model_name_or_path, config, cache_dir, backend, is_peft_model, **model_args)
  File "/usr/local/lib/python3.10/dist-packages/sentence_transformers/models/Transformer.py", line 181, in _load_model
    self.auto_model = AutoModel.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/models/auto/auto_factory.py", line 571, in from_pretrained
    return model_class.from_pretrained(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 279, in _wrapper
    return func(*args, **kwargs)
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4400, in from_pretrained
    ) = cls._load_pretrained_model(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 4659, in _load_pretrained_model
    missing_keys, unexpected_keys = _find_missing_and_unexpected_keys(
  File "/usr/local/lib/python3.10/dist-packages/transformers/modeling_utils.py", line 1352, in _find_missing_and_unexpected_keys
    tied_params = find_tied_parameters(model)
NameError: name 'find_tied_parameters' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/mnt/extra-addons/custom-addons/17.0/odoo_law_agents/tools/embedding_helper.py", line 89, in generate_embedding
    fallback_model = 'all-MiniLM-L6-v2'  # A smaller, more compatible model
Exception: Failed to load any embedding model: name 'find_tied_parameters' is not defined

2025-04-06 10:24:21,321 - INFO - Writing result to stdout
2025-04-06 10:24:21,321 - INFO - Result written successfully
2025-04-06 10:24:22,662 - INFO - Starting embedding helper script
2025-04-06 10:24:22,662 - INFO - Reading input from stdin
2025-04-06 10:24:22,663 - INFO - Input data length: 9313
2025-04-06 10:24:22,663 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1902
2025-04-06 10:24:22,663 - INFO - Processing text with length: 1902 chars
2025-04-06 10:24:22,664 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:24:22,664 - INFO - Text length: 1902
2025-04-06 10:24:25,791 - INFO - Torch threads set to 1
2025-04-06 10:24:31,782 - INFO - Loading model...
2025-04-06 10:24:31,782 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:24:31,783 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:24:31,784 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:24:43,546 - INFO - Model loaded successfully
2025-04-06 10:24:43,546 - INFO - Generating embedding...
2025-04-06 10:24:44,250 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:24:44,251 - INFO - Returning successful result
2025-04-06 10:24:44,252 - INFO - Writing result to stdout
2025-04-06 10:24:44,252 - INFO - Result written successfully
2025-04-06 10:24:46,052 - INFO - Starting embedding helper script
2025-04-06 10:24:46,052 - INFO - Reading input from stdin
2025-04-06 10:24:46,053 - INFO - Input data length: 9129
2025-04-06 10:24:46,053 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1853
2025-04-06 10:24:46,053 - INFO - Processing text with length: 1853 chars
2025-04-06 10:24:46,054 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:24:46,054 - INFO - Text length: 1853
2025-04-06 10:24:48,870 - INFO - Torch threads set to 1
2025-04-06 10:24:54,622 - INFO - Loading model...
2025-04-06 10:24:54,622 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:24:54,623 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:24:54,625 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:25:00,539 - INFO - Model loaded successfully
2025-04-06 10:25:00,540 - INFO - Generating embedding...
2025-04-06 10:25:00,733 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:25:00,734 - INFO - Returning successful result
2025-04-06 10:25:00,735 - INFO - Writing result to stdout
2025-04-06 10:25:00,735 - INFO - Result written successfully
2025-04-06 10:29:54,257 - INFO - Starting embedding helper script
2025-04-06 10:29:54,260 - INFO - Reading input from stdin
2025-04-06 10:29:54,275 - INFO - Input data length: 9090
2025-04-06 10:29:54,276 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1884
2025-04-06 10:29:54,276 - INFO - Processing text with length: 1884 chars
2025-04-06 10:29:54,277 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:29:54,277 - INFO - Text length: 1884
2025-04-06 10:29:57,640 - INFO - Torch threads set to 1
2025-04-06 10:30:03,784 - INFO - Loading model...
2025-04-06 10:30:03,784 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:30:03,785 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:30:03,786 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:30:09,408 - INFO - Model loaded successfully
2025-04-06 10:30:09,408 - INFO - Generating embedding...
2025-04-06 10:30:09,707 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:30:09,708 - INFO - Returning successful result
2025-04-06 10:30:09,709 - INFO - Writing result to stdout
2025-04-06 10:30:09,710 - INFO - Result written successfully
2025-04-06 10:30:11,334 - INFO - Starting embedding helper script
2025-04-06 10:30:11,335 - INFO - Reading input from stdin
2025-04-06 10:30:11,335 - INFO - Input data length: 9477
2025-04-06 10:30:11,336 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1966
2025-04-06 10:30:11,336 - INFO - Processing text with length: 1966 chars
2025-04-06 10:30:11,336 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:30:11,337 - INFO - Text length: 1966
2025-04-06 10:30:14,083 - INFO - Torch threads set to 1
2025-04-06 10:30:18,904 - INFO - Loading model...
2025-04-06 10:30:18,904 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:30:18,905 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:30:18,906 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:30:24,113 - INFO - Model loaded successfully
2025-04-06 10:30:24,113 - INFO - Generating embedding...
2025-04-06 10:30:24,263 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:30:24,264 - INFO - Returning successful result
2025-04-06 10:30:24,264 - INFO - Writing result to stdout
2025-04-06 10:30:24,265 - INFO - Result written successfully
2025-04-06 10:30:25,541 - INFO - Starting embedding helper script
2025-04-06 10:30:25,541 - INFO - Reading input from stdin
2025-04-06 10:30:25,542 - INFO - Input data length: 8516
2025-04-06 10:30:25,542 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1695
2025-04-06 10:30:25,542 - INFO - Processing text with length: 1695 chars
2025-04-06 10:30:25,543 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:30:25,543 - INFO - Text length: 1695
2025-04-06 10:30:28,226 - INFO - Torch threads set to 1
2025-04-06 10:30:32,777 - INFO - Loading model...
2025-04-06 10:30:32,777 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:30:32,778 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:30:32,778 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:30:37,962 - INFO - Model loaded successfully
2025-04-06 10:30:37,962 - INFO - Generating embedding...
2025-04-06 10:30:38,112 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:30:38,113 - INFO - Returning successful result
2025-04-06 10:30:38,113 - INFO - Writing result to stdout
2025-04-06 10:30:38,113 - INFO - Result written successfully
2025-04-06 10:30:39,337 - INFO - Starting embedding helper script
2025-04-06 10:30:39,337 - INFO - Reading input from stdin
2025-04-06 10:30:39,338 - INFO - Input data length: 9589
2025-04-06 10:30:39,338 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1963
2025-04-06 10:30:39,339 - INFO - Processing text with length: 1963 chars
2025-04-06 10:30:39,339 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:30:39,339 - INFO - Text length: 1963
2025-04-06 10:30:41,600 - INFO - Torch threads set to 1
2025-04-06 10:30:46,379 - INFO - Loading model...
2025-04-06 10:30:46,380 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:30:46,380 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:30:46,381 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:30:52,236 - INFO - Model loaded successfully
2025-04-06 10:30:52,236 - INFO - Generating embedding...
2025-04-06 10:30:52,379 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:30:52,380 - INFO - Returning successful result
2025-04-06 10:30:52,380 - INFO - Writing result to stdout
2025-04-06 10:30:52,381 - INFO - Result written successfully
2025-04-06 10:30:53,580 - INFO - Starting embedding helper script
2025-04-06 10:30:53,581 - INFO - Reading input from stdin
2025-04-06 10:30:53,581 - INFO - Input data length: 9573
2025-04-06 10:30:53,581 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1967
2025-04-06 10:30:53,582 - INFO - Processing text with length: 1967 chars
2025-04-06 10:30:53,582 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:30:53,582 - INFO - Text length: 1967
2025-04-06 10:30:55,695 - INFO - Torch threads set to 1
2025-04-06 10:31:01,409 - INFO - Loading model...
2025-04-06 10:31:01,409 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:31:01,410 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:31:01,410 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:31:06,417 - INFO - Model loaded successfully
2025-04-06 10:31:06,417 - INFO - Generating embedding...
2025-04-06 10:31:06,560 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:31:06,561 - INFO - Returning successful result
2025-04-06 10:31:06,562 - INFO - Writing result to stdout
2025-04-06 10:31:06,562 - INFO - Result written successfully
2025-04-06 10:31:07,826 - INFO - Starting embedding helper script
2025-04-06 10:31:07,827 - INFO - Reading input from stdin
2025-04-06 10:31:07,827 - INFO - Input data length: 9313
2025-04-06 10:31:07,827 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1902
2025-04-06 10:31:07,828 - INFO - Processing text with length: 1902 chars
2025-04-06 10:31:07,828 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:31:07,828 - INFO - Text length: 1902
2025-04-06 10:31:11,471 - INFO - Torch threads set to 1
2025-04-06 10:31:16,072 - INFO - Loading model...
2025-04-06 10:31:16,072 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:31:16,073 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:31:16,073 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:31:21,331 - INFO - Model loaded successfully
2025-04-06 10:31:21,331 - INFO - Generating embedding...
2025-04-06 10:31:21,471 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:31:21,472 - INFO - Returning successful result
2025-04-06 10:31:21,472 - INFO - Writing result to stdout
2025-04-06 10:31:21,472 - INFO - Result written successfully
2025-04-06 10:31:22,597 - INFO - Starting embedding helper script
2025-04-06 10:31:22,598 - INFO - Reading input from stdin
2025-04-06 10:31:22,598 - INFO - Input data length: 9129
2025-04-06 10:31:22,598 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1853
2025-04-06 10:31:22,599 - INFO - Processing text with length: 1853 chars
2025-04-06 10:31:22,599 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:31:22,600 - INFO - Text length: 1853
2025-04-06 10:31:24,745 - INFO - Torch threads set to 1
2025-04-06 10:31:29,184 - INFO - Loading model...
2025-04-06 10:31:29,184 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:31:29,184 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:31:29,185 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:31:34,651 - INFO - Model loaded successfully
2025-04-06 10:31:34,651 - INFO - Generating embedding...
2025-04-06 10:31:34,791 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:31:34,792 - INFO - Returning successful result
2025-04-06 10:31:34,793 - INFO - Writing result to stdout
2025-04-06 10:31:34,793 - INFO - Result written successfully
2025-04-06 10:31:36,005 - INFO - Starting embedding helper script
2025-04-06 10:31:36,005 - INFO - Reading input from stdin
2025-04-06 10:31:36,005 - INFO - Input data length: 4649
2025-04-06 10:31:36,006 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=918
2025-04-06 10:31:36,006 - INFO - Processing text with length: 918 chars
2025-04-06 10:31:36,006 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:31:36,007 - INFO - Text length: 918
2025-04-06 10:31:38,792 - INFO - Torch threads set to 1
2025-04-06 10:31:44,520 - INFO - Loading model...
2025-04-06 10:31:44,520 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:31:44,520 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:31:44,521 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:31:50,178 - INFO - Model loaded successfully
2025-04-06 10:31:50,179 - INFO - Generating embedding...
2025-04-06 10:31:50,290 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:31:50,291 - INFO - Returning successful result
2025-04-06 10:31:50,292 - INFO - Writing result to stdout
2025-04-06 10:31:50,292 - INFO - Result written successfully
2025-04-06 10:31:51,570 - INFO - Starting embedding helper script
2025-04-06 10:31:51,570 - INFO - Reading input from stdin
2025-04-06 10:31:51,571 - INFO - Input data length: 9487
2025-04-06 10:31:51,571 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1991
2025-04-06 10:31:51,572 - INFO - Processing text with length: 1991 chars
2025-04-06 10:31:51,572 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:31:51,572 - INFO - Text length: 1991
2025-04-06 10:31:55,820 - INFO - Torch threads set to 1
2025-04-06 10:32:01,492 - INFO - Loading model...
2025-04-06 10:32:01,492 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:32:01,493 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:32:01,494 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:32:07,669 - INFO - Model loaded successfully
2025-04-06 10:32:07,670 - INFO - Generating embedding...
2025-04-06 10:32:08,238 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:32:08,239 - INFO - Returning successful result
2025-04-06 10:32:08,240 - INFO - Writing result to stdout
2025-04-06 10:32:08,241 - INFO - Result written successfully
2025-04-06 10:32:13,563 - INFO - Starting embedding helper script
2025-04-06 10:32:13,585 - INFO - Reading input from stdin
2025-04-06 10:32:13,586 - INFO - Input data length: 9582
2025-04-06 10:32:13,586 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1976
2025-04-06 10:32:13,587 - INFO - Processing text with length: 1976 chars
2025-04-06 10:32:13,588 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:32:13,591 - INFO - Text length: 1976
2025-04-06 10:32:18,246 - INFO - Torch threads set to 1
2025-04-06 10:32:33,499 - INFO - Loading model...
2025-04-06 10:32:33,500 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:32:33,500 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:32:33,502 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:32:39,269 - INFO - Model loaded successfully
2025-04-06 10:32:39,269 - INFO - Generating embedding...
2025-04-06 10:32:39,523 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:32:39,526 - INFO - Returning successful result
2025-04-06 10:32:39,527 - INFO - Writing result to stdout
2025-04-06 10:32:39,528 - INFO - Result written successfully
2025-04-06 10:32:41,809 - INFO - Starting embedding helper script
2025-04-06 10:32:41,810 - INFO - Reading input from stdin
2025-04-06 10:32:41,810 - INFO - Input data length: 8502
2025-04-06 10:32:41,811 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1731
2025-04-06 10:32:41,811 - INFO - Processing text with length: 1731 chars
2025-04-06 10:32:41,811 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:32:41,812 - INFO - Text length: 1731
2025-04-06 10:32:44,772 - INFO - Torch threads set to 1
2025-04-06 10:32:53,567 - INFO - Loading model...
2025-04-06 10:32:53,568 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:32:53,568 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:32:53,571 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:32:59,308 - INFO - Model loaded successfully
2025-04-06 10:32:59,312 - INFO - Generating embedding...
2025-04-06 10:32:59,959 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:32:59,961 - INFO - Returning successful result
2025-04-06 10:33:00,022 - INFO - Writing result to stdout
2025-04-06 10:33:00,023 - INFO - Result written successfully
2025-04-06 10:33:03,704 - INFO - Starting embedding helper script
2025-04-06 10:33:03,707 - INFO - Reading input from stdin
2025-04-06 10:33:03,708 - INFO - Input data length: 9012
2025-04-06 10:33:03,708 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1886
2025-04-06 10:33:03,709 - INFO - Processing text with length: 1886 chars
2025-04-06 10:33:03,709 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:33:03,710 - INFO - Text length: 1886
2025-04-06 10:33:11,728 - INFO - Torch threads set to 1
2025-04-06 10:33:27,983 - INFO - Loading model...
2025-04-06 10:33:27,985 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:33:27,986 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:33:27,989 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:33:34,182 - INFO - Model loaded successfully
2025-04-06 10:33:34,183 - INFO - Generating embedding...
2025-04-06 10:33:34,466 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:33:34,467 - INFO - Returning successful result
2025-04-06 10:33:34,468 - INFO - Writing result to stdout
2025-04-06 10:33:34,469 - INFO - Result written successfully
2025-04-06 10:33:36,384 - INFO - Starting embedding helper script
2025-04-06 10:33:36,385 - INFO - Reading input from stdin
2025-04-06 10:33:36,386 - INFO - Input data length: 7035
2025-04-06 10:33:36,386 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1434
2025-04-06 10:33:36,387 - INFO - Processing text with length: 1434 chars
2025-04-06 10:33:36,387 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:33:36,388 - INFO - Text length: 1434
2025-04-06 10:33:40,258 - INFO - Torch threads set to 1
2025-04-06 10:33:48,659 - INFO - Loading model...
2025-04-06 10:33:48,659 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:33:48,660 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:33:48,661 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:33:54,232 - INFO - Model loaded successfully
2025-04-06 10:33:54,233 - INFO - Generating embedding...
2025-04-06 10:33:54,415 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:33:54,416 - INFO - Returning successful result
2025-04-06 10:33:54,417 - INFO - Writing result to stdout
2025-04-06 10:33:54,419 - INFO - Result written successfully
2025-04-06 10:33:56,028 - INFO - Starting embedding helper script
2025-04-06 10:33:56,028 - INFO - Reading input from stdin
2025-04-06 10:33:56,029 - INFO - Input data length: 9090
2025-04-06 10:33:56,030 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1884
2025-04-06 10:33:56,030 - INFO - Processing text with length: 1884 chars
2025-04-06 10:33:56,030 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:33:56,031 - INFO - Text length: 1884
2025-04-06 10:33:58,735 - INFO - Torch threads set to 1
2025-04-06 10:34:04,485 - INFO - Loading model...
2025-04-06 10:34:04,485 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:34:04,485 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:34:04,486 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:34:09,747 - INFO - Model loaded successfully
2025-04-06 10:34:09,748 - INFO - Generating embedding...
2025-04-06 10:34:09,983 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:34:09,984 - INFO - Returning successful result
2025-04-06 10:34:09,984 - INFO - Writing result to stdout
2025-04-06 10:34:09,984 - INFO - Result written successfully
2025-04-06 10:34:11,536 - INFO - Starting embedding helper script
2025-04-06 10:34:11,536 - INFO - Reading input from stdin
2025-04-06 10:34:11,536 - INFO - Input data length: 9477
2025-04-06 10:34:11,537 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1966
2025-04-06 10:34:11,537 - INFO - Processing text with length: 1966 chars
2025-04-06 10:34:11,538 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:34:11,538 - INFO - Text length: 1966
2025-04-06 10:34:14,158 - INFO - Torch threads set to 1
2025-04-06 10:34:19,346 - INFO - Loading model...
2025-04-06 10:34:19,347 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:34:19,348 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:34:19,349 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:34:24,334 - INFO - Model loaded successfully
2025-04-06 10:34:24,335 - INFO - Generating embedding...
2025-04-06 10:34:24,490 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:34:24,491 - INFO - Returning successful result
2025-04-06 10:34:24,491 - INFO - Writing result to stdout
2025-04-06 10:34:24,492 - INFO - Result written successfully
2025-04-06 10:34:25,811 - INFO - Starting embedding helper script
2025-04-06 10:34:25,811 - INFO - Reading input from stdin
2025-04-06 10:34:25,812 - INFO - Input data length: 8516
2025-04-06 10:34:25,812 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1695
2025-04-06 10:34:25,812 - INFO - Processing text with length: 1695 chars
2025-04-06 10:34:25,813 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:34:25,813 - INFO - Text length: 1695
2025-04-06 10:34:28,038 - INFO - Torch threads set to 1
2025-04-06 10:34:33,560 - INFO - Loading model...
2025-04-06 10:34:33,561 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:34:33,561 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:34:33,562 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:34:38,563 - INFO - Model loaded successfully
2025-04-06 10:34:38,563 - INFO - Generating embedding...
2025-04-06 10:34:38,712 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:34:38,712 - INFO - Returning successful result
2025-04-06 10:34:38,713 - INFO - Writing result to stdout
2025-04-06 10:34:38,713 - INFO - Result written successfully
2025-04-06 10:34:40,044 - INFO - Starting embedding helper script
2025-04-06 10:34:40,044 - INFO - Reading input from stdin
2025-04-06 10:34:40,045 - INFO - Input data length: 9773
2025-04-06 10:34:40,045 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1992
2025-04-06 10:34:40,045 - INFO - Processing text with length: 1992 chars
2025-04-06 10:34:40,046 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:34:40,046 - INFO - Text length: 1992
2025-04-06 10:34:42,455 - INFO - Torch threads set to 1
2025-04-06 10:34:47,506 - INFO - Loading model...
2025-04-06 10:34:47,507 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:34:47,507 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:34:47,508 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:34:52,764 - INFO - Model loaded successfully
2025-04-06 10:34:52,765 - INFO - Generating embedding...
2025-04-06 10:34:52,955 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:34:52,956 - INFO - Returning successful result
2025-04-06 10:34:52,957 - INFO - Writing result to stdout
2025-04-06 10:34:52,957 - INFO - Result written successfully
2025-04-06 10:34:54,675 - INFO - Starting embedding helper script
2025-04-06 10:34:54,675 - INFO - Reading input from stdin
2025-04-06 10:34:54,675 - INFO - Input data length: 9512
2025-04-06 10:34:54,676 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1931
2025-04-06 10:34:54,676 - INFO - Processing text with length: 1931 chars
2025-04-06 10:34:54,677 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:34:54,677 - INFO - Text length: 1931
2025-04-06 10:34:57,102 - INFO - Torch threads set to 1
2025-04-06 10:35:02,601 - INFO - Loading model...
2025-04-06 10:35:02,602 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:35:02,602 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:35:02,603 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:35:07,613 - INFO - Model loaded successfully
2025-04-06 10:35:07,613 - INFO - Generating embedding...
2025-04-06 10:35:07,767 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:35:07,768 - INFO - Returning successful result
2025-04-06 10:35:07,768 - INFO - Writing result to stdout
2025-04-06 10:35:07,768 - INFO - Result written successfully
2025-04-06 10:35:09,177 - INFO - Starting embedding helper script
2025-04-06 10:35:09,178 - INFO - Reading input from stdin
2025-04-06 10:35:09,178 - INFO - Input data length: 9900
2025-04-06 10:35:09,178 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1994
2025-04-06 10:35:09,179 - INFO - Processing text with length: 1994 chars
2025-04-06 10:35:09,179 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:35:09,179 - INFO - Text length: 1994
2025-04-06 10:35:11,490 - INFO - Torch threads set to 1
2025-04-06 10:35:16,354 - INFO - Loading model...
2025-04-06 10:35:16,354 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:35:16,355 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:35:16,356 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:35:22,469 - INFO - Model loaded successfully
2025-04-06 10:35:22,470 - INFO - Generating embedding...
2025-04-06 10:35:22,777 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:35:22,785 - INFO - Returning successful result
2025-04-06 10:35:22,785 - INFO - Writing result to stdout
2025-04-06 10:35:22,786 - INFO - Result written successfully
2025-04-06 10:35:25,758 - INFO - Starting embedding helper script
2025-04-06 10:35:25,758 - INFO - Reading input from stdin
2025-04-06 10:35:25,759 - INFO - Input data length: 9044
2025-04-06 10:35:25,759 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1943
2025-04-06 10:35:25,760 - INFO - Processing text with length: 1943 chars
2025-04-06 10:35:25,760 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:35:25,760 - INFO - Text length: 1943
2025-04-06 10:35:29,607 - INFO - Torch threads set to 1
2025-04-06 10:35:36,328 - INFO - Loading model...
2025-04-06 10:35:36,329 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:35:36,329 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:35:36,331 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:35:41,891 - INFO - Model loaded successfully
2025-04-06 10:35:41,891 - INFO - Generating embedding...
2025-04-06 10:35:42,126 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:35:42,127 - INFO - Returning successful result
2025-04-06 10:35:42,127 - INFO - Writing result to stdout
2025-04-06 10:35:42,127 - INFO - Result written successfully
2025-04-06 10:35:43,804 - INFO - Starting embedding helper script
2025-04-06 10:35:43,805 - INFO - Reading input from stdin
2025-04-06 10:35:43,805 - INFO - Input data length: 6230
2025-04-06 10:35:43,805 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1244
2025-04-06 10:35:43,806 - INFO - Processing text with length: 1244 chars
2025-04-06 10:35:43,806 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:35:43,806 - INFO - Text length: 1244
2025-04-06 10:35:46,203 - INFO - Torch threads set to 1
2025-04-06 10:35:51,595 - INFO - Loading model...
2025-04-06 10:35:51,595 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:35:51,596 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:35:51,597 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:35:56,990 - INFO - Model loaded successfully
2025-04-06 10:35:56,990 - INFO - Generating embedding...
2025-04-06 10:35:57,136 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:35:57,137 - INFO - Returning successful result
2025-04-06 10:35:57,137 - INFO - Writing result to stdout
2025-04-06 10:35:57,138 - INFO - Result written successfully
2025-04-06 10:35:58,484 - INFO - Starting embedding helper script
2025-04-06 10:35:58,484 - INFO - Reading input from stdin
2025-04-06 10:35:58,484 - INFO - Input data length: 9413
2025-04-06 10:35:58,485 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1947
2025-04-06 10:35:58,485 - INFO - Processing text with length: 1947 chars
2025-04-06 10:35:58,485 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:35:58,486 - INFO - Text length: 1947
2025-04-06 10:36:00,918 - INFO - Torch threads set to 1
2025-04-06 10:36:05,871 - INFO - Loading model...
2025-04-06 10:36:05,871 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:36:05,871 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:36:05,872 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:36:12,381 - INFO - Model loaded successfully
2025-04-06 10:36:12,382 - INFO - Generating embedding...
2025-04-06 10:36:12,556 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:36:12,557 - INFO - Returning successful result
2025-04-06 10:36:12,557 - INFO - Writing result to stdout
2025-04-06 10:36:12,557 - INFO - Result written successfully
2025-04-06 10:36:13,889 - INFO - Starting embedding helper script
2025-04-06 10:36:13,890 - INFO - Reading input from stdin
2025-04-06 10:36:13,890 - INFO - Input data length: 9031
2025-04-06 10:36:13,891 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1840
2025-04-06 10:36:13,891 - INFO - Processing text with length: 1840 chars
2025-04-06 10:36:13,891 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-06 10:36:13,891 - INFO - Text length: 1840
2025-04-06 10:36:16,117 - INFO - Torch threads set to 1
2025-04-06 10:36:21,012 - INFO - Loading model...
2025-04-06 10:36:21,012 - INFO - Added fallback init_empty_weights implementation
2025-04-06 10:36:21,012 - INFO - Added fallback find_tied_parameters implementation
2025-04-06 10:36:21,014 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-06 10:36:26,072 - INFO - Model loaded successfully
2025-04-06 10:36:26,072 - INFO - Generating embedding...
2025-04-06 10:36:26,222 - INFO - Embedding generated successfully with shape: (384,)
2025-04-06 10:36:26,223 - INFO - Returning successful result
2025-04-06 10:36:26,223 - INFO - Writing result to stdout
2025-04-06 10:36:26,223 - INFO - Result written successfully
2025-04-07 14:51:19,607 - INFO - Starting embedding helper script
2025-04-07 14:51:19,608 - INFO - Reading input from stdin
2025-04-07 14:51:19,609 - INFO - Input data length: 79
2025-04-07 14:51:19,609 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=33
2025-04-07 14:51:19,609 - INFO - Processing text with length: 33 chars
2025-04-07 14:51:19,610 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 14:51:19,610 - INFO - Text length: 33
2025-04-07 14:51:22,151 - INFO - Torch threads set to 1
2025-04-07 14:51:26,862 - INFO - Loading model...
2025-04-07 14:51:26,862 - INFO - Added fallback init_empty_weights implementation
2025-04-07 14:51:26,863 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 14:51:26,863 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 14:51:46,291 - INFO - Model loaded successfully
2025-04-07 14:51:46,292 - INFO - Generating embedding...
2025-04-07 14:51:46,403 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 14:51:46,404 - INFO - Returning successful result
2025-04-07 14:51:46,405 - INFO - Writing result to stdout
2025-04-07 14:51:46,405 - INFO - Result written successfully
2025-04-07 14:51:47,612 - INFO - Starting embedding helper script
2025-04-07 14:51:47,612 - INFO - Reading input from stdin
2025-04-07 14:51:47,612 - INFO - Input data length: 78
2025-04-07 14:51:47,613 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=32
2025-04-07 14:51:47,613 - INFO - Processing text with length: 32 chars
2025-04-07 14:51:47,613 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 14:51:47,614 - INFO - Text length: 32
2025-04-07 14:51:49,837 - INFO - Torch threads set to 1
2025-04-07 14:51:54,220 - INFO - Loading model...
2025-04-07 14:51:54,221 - INFO - Added fallback init_empty_weights implementation
2025-04-07 14:51:54,221 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 14:51:54,222 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 14:51:58,544 - INFO - Model loaded successfully
2025-04-07 14:51:58,544 - INFO - Generating embedding...
2025-04-07 14:51:58,564 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 14:51:58,565 - INFO - Returning successful result
2025-04-07 14:51:58,565 - INFO - Writing result to stdout
2025-04-07 14:51:58,566 - INFO - Result written successfully
2025-04-07 14:51:59,780 - INFO - Starting embedding helper script
2025-04-07 14:51:59,780 - INFO - Reading input from stdin
2025-04-07 14:51:59,780 - INFO - Input data length: 71
2025-04-07 14:51:59,781 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=25
2025-04-07 14:51:59,781 - INFO - Processing text with length: 25 chars
2025-04-07 14:51:59,781 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 14:51:59,782 - INFO - Text length: 25
2025-04-07 14:52:01,954 - INFO - Torch threads set to 1
2025-04-07 14:52:06,319 - INFO - Loading model...
2025-04-07 14:52:06,319 - INFO - Added fallback init_empty_weights implementation
2025-04-07 14:52:06,319 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 14:52:06,320 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 14:52:09,599 - INFO - Model loaded successfully
2025-04-07 14:52:09,599 - INFO - Generating embedding...
2025-04-07 14:52:09,619 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 14:52:09,620 - INFO - Returning successful result
2025-04-07 14:52:09,620 - INFO - Writing result to stdout
2025-04-07 14:52:09,620 - INFO - Result written successfully
2025-04-07 14:52:10,851 - INFO - Starting embedding helper script
2025-04-07 14:52:10,851 - INFO - Reading input from stdin
2025-04-07 14:52:10,852 - INFO - Input data length: 77
2025-04-07 14:52:10,852 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=31
2025-04-07 14:52:10,853 - INFO - Processing text with length: 31 chars
2025-04-07 14:52:10,853 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 14:52:10,853 - INFO - Text length: 31
2025-04-07 14:52:13,154 - INFO - Torch threads set to 1
2025-04-07 14:52:17,640 - INFO - Loading model...
2025-04-07 14:52:17,640 - INFO - Added fallback init_empty_weights implementation
2025-04-07 14:52:17,640 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 14:52:17,641 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 14:52:20,834 - INFO - Model loaded successfully
2025-04-07 14:52:20,835 - INFO - Generating embedding...
2025-04-07 14:52:20,855 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 14:52:20,856 - INFO - Returning successful result
2025-04-07 14:52:20,856 - INFO - Writing result to stdout
2025-04-07 14:52:20,857 - INFO - Result written successfully
2025-04-07 14:52:22,063 - INFO - Starting embedding helper script
2025-04-07 14:52:22,063 - INFO - Reading input from stdin
2025-04-07 14:52:22,064 - INFO - Input data length: 73
2025-04-07 14:52:22,064 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=27
2025-04-07 14:52:22,064 - INFO - Processing text with length: 27 chars
2025-04-07 14:52:22,064 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 14:52:22,065 - INFO - Text length: 27
2025-04-07 14:52:24,218 - INFO - Torch threads set to 1
2025-04-07 14:52:28,717 - INFO - Loading model...
2025-04-07 14:52:28,717 - INFO - Added fallback init_empty_weights implementation
2025-04-07 14:52:28,717 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 14:52:28,718 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 14:52:31,866 - INFO - Model loaded successfully
2025-04-07 14:52:31,867 - INFO - Generating embedding...
2025-04-07 14:52:31,943 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 14:52:31,944 - INFO - Returning successful result
2025-04-07 14:52:31,944 - INFO - Writing result to stdout
2025-04-07 14:52:31,944 - INFO - Result written successfully
2025-04-07 14:58:50,389 - INFO - Starting embedding helper script
2025-04-07 14:58:50,390 - INFO - Reading input from stdin
2025-04-07 14:58:50,390 - INFO - Input data length: 79
2025-04-07 14:58:50,391 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=33
2025-04-07 14:58:50,391 - INFO - Processing text with length: 33 chars
2025-04-07 14:58:50,391 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 14:58:50,392 - INFO - Text length: 33
2025-04-07 14:58:52,866 - INFO - Torch threads set to 1
2025-04-07 14:58:57,565 - INFO - Loading model...
2025-04-07 14:58:57,566 - INFO - Added fallback init_empty_weights implementation
2025-04-07 14:58:57,566 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 14:58:57,567 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 14:59:00,483 - INFO - Model loaded successfully
2025-04-07 14:59:00,484 - INFO - Generating embedding...
2025-04-07 14:59:00,506 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 14:59:00,507 - INFO - Returning successful result
2025-04-07 14:59:00,507 - INFO - Writing result to stdout
2025-04-07 14:59:00,508 - INFO - Result written successfully
2025-04-07 14:59:01,757 - INFO - Starting embedding helper script
2025-04-07 14:59:01,757 - INFO - Reading input from stdin
2025-04-07 14:59:01,758 - INFO - Input data length: 78
2025-04-07 14:59:01,758 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=32
2025-04-07 14:59:01,758 - INFO - Processing text with length: 32 chars
2025-04-07 14:59:01,759 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 14:59:01,759 - INFO - Text length: 32
2025-04-07 14:59:04,249 - INFO - Torch threads set to 1
2025-04-07 14:59:08,912 - INFO - Loading model...
2025-04-07 14:59:08,913 - INFO - Added fallback init_empty_weights implementation
2025-04-07 14:59:08,913 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 14:59:08,914 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 14:59:11,763 - INFO - Model loaded successfully
2025-04-07 14:59:11,764 - INFO - Generating embedding...
2025-04-07 14:59:11,785 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 14:59:11,786 - INFO - Returning successful result
2025-04-07 14:59:11,786 - INFO - Writing result to stdout
2025-04-07 14:59:11,786 - INFO - Result written successfully
2025-04-07 14:59:13,047 - INFO - Starting embedding helper script
2025-04-07 14:59:13,048 - INFO - Reading input from stdin
2025-04-07 14:59:13,048 - INFO - Input data length: 71
2025-04-07 14:59:13,049 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=25
2025-04-07 14:59:13,049 - INFO - Processing text with length: 25 chars
2025-04-07 14:59:13,049 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 14:59:13,050 - INFO - Text length: 25
2025-04-07 14:59:15,241 - INFO - Torch threads set to 1
2025-04-07 14:59:20,226 - INFO - Loading model...
2025-04-07 14:59:20,226 - INFO - Added fallback init_empty_weights implementation
2025-04-07 14:59:20,226 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 14:59:20,227 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 14:59:23,073 - INFO - Model loaded successfully
2025-04-07 14:59:23,074 - INFO - Generating embedding...
2025-04-07 14:59:23,093 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 14:59:23,094 - INFO - Returning successful result
2025-04-07 14:59:23,095 - INFO - Writing result to stdout
2025-04-07 14:59:23,096 - INFO - Result written successfully
2025-04-07 14:59:24,322 - INFO - Starting embedding helper script
2025-04-07 14:59:24,322 - INFO - Reading input from stdin
2025-04-07 14:59:24,323 - INFO - Input data length: 77
2025-04-07 14:59:24,323 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=31
2025-04-07 14:59:24,324 - INFO - Processing text with length: 31 chars
2025-04-07 14:59:24,324 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 14:59:24,324 - INFO - Text length: 31
2025-04-07 14:59:26,571 - INFO - Torch threads set to 1
2025-04-07 14:59:31,174 - INFO - Loading model...
2025-04-07 14:59:31,174 - INFO - Added fallback init_empty_weights implementation
2025-04-07 14:59:31,174 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 14:59:31,175 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 14:59:34,010 - INFO - Model loaded successfully
2025-04-07 14:59:34,010 - INFO - Generating embedding...
2025-04-07 14:59:34,030 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 14:59:34,031 - INFO - Returning successful result
2025-04-07 14:59:34,032 - INFO - Writing result to stdout
2025-04-07 14:59:34,032 - INFO - Result written successfully
2025-04-07 14:59:35,314 - INFO - Starting embedding helper script
2025-04-07 14:59:35,315 - INFO - Reading input from stdin
2025-04-07 14:59:35,315 - INFO - Input data length: 73
2025-04-07 14:59:35,315 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=27
2025-04-07 14:59:35,316 - INFO - Processing text with length: 27 chars
2025-04-07 14:59:35,316 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 14:59:35,316 - INFO - Text length: 27
2025-04-07 14:59:37,685 - INFO - Torch threads set to 1
2025-04-07 14:59:42,118 - INFO - Loading model...
2025-04-07 14:59:42,118 - INFO - Added fallback init_empty_weights implementation
2025-04-07 14:59:42,118 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 14:59:42,119 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 14:59:45,033 - INFO - Model loaded successfully
2025-04-07 14:59:45,034 - INFO - Generating embedding...
2025-04-07 14:59:45,055 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 14:59:45,055 - INFO - Returning successful result
2025-04-07 14:59:45,056 - INFO - Writing result to stdout
2025-04-07 14:59:45,056 - INFO - Result written successfully
2025-04-07 15:06:30,458 - INFO - Starting embedding helper script
2025-04-07 15:06:30,459 - INFO - Reading input from stdin
2025-04-07 15:06:30,459 - INFO - Input data length: 79
2025-04-07 15:06:30,460 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=33
2025-04-07 15:06:30,460 - INFO - Processing text with length: 33 chars
2025-04-07 15:06:30,461 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:06:30,461 - INFO - Text length: 33
2025-04-07 15:06:32,781 - INFO - Torch threads set to 1
2025-04-07 15:06:37,249 - INFO - Loading model...
2025-04-07 15:06:37,249 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:06:37,250 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:06:37,250 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:06:40,297 - INFO - Model loaded successfully
2025-04-07 15:06:40,297 - INFO - Generating embedding...
2025-04-07 15:06:40,337 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:06:40,339 - INFO - Returning successful result
2025-04-07 15:06:40,339 - INFO - Writing result to stdout
2025-04-07 15:06:40,340 - INFO - Result written successfully
2025-04-07 15:06:41,630 - INFO - Starting embedding helper script
2025-04-07 15:06:41,631 - INFO - Reading input from stdin
2025-04-07 15:06:41,631 - INFO - Input data length: 78
2025-04-07 15:06:41,632 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=32
2025-04-07 15:06:41,632 - INFO - Processing text with length: 32 chars
2025-04-07 15:06:41,632 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:06:41,632 - INFO - Text length: 32
2025-04-07 15:06:44,006 - INFO - Torch threads set to 1
2025-04-07 15:06:48,491 - INFO - Loading model...
2025-04-07 15:06:48,491 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:06:48,492 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:06:48,492 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:06:51,554 - INFO - Model loaded successfully
2025-04-07 15:06:51,554 - INFO - Generating embedding...
2025-04-07 15:06:51,574 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:06:51,575 - INFO - Returning successful result
2025-04-07 15:06:51,576 - INFO - Writing result to stdout
2025-04-07 15:06:51,576 - INFO - Result written successfully
2025-04-07 15:06:52,873 - INFO - Starting embedding helper script
2025-04-07 15:06:52,874 - INFO - Reading input from stdin
2025-04-07 15:06:52,874 - INFO - Input data length: 71
2025-04-07 15:06:52,875 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=25
2025-04-07 15:06:52,875 - INFO - Processing text with length: 25 chars
2025-04-07 15:06:52,875 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:06:52,875 - INFO - Text length: 25
2025-04-07 15:06:55,147 - INFO - Torch threads set to 1
2025-04-07 15:06:59,680 - INFO - Loading model...
2025-04-07 15:06:59,682 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:06:59,682 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:06:59,683 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:07:02,606 - INFO - Model loaded successfully
2025-04-07 15:07:02,606 - INFO - Generating embedding...
2025-04-07 15:07:02,626 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:07:02,627 - INFO - Returning successful result
2025-04-07 15:07:02,628 - INFO - Writing result to stdout
2025-04-07 15:07:02,628 - INFO - Result written successfully
2025-04-07 15:07:03,842 - INFO - Starting embedding helper script
2025-04-07 15:07:03,842 - INFO - Reading input from stdin
2025-04-07 15:07:03,843 - INFO - Input data length: 77
2025-04-07 15:07:03,843 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=31
2025-04-07 15:07:03,844 - INFO - Processing text with length: 31 chars
2025-04-07 15:07:03,844 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:07:03,844 - INFO - Text length: 31
2025-04-07 15:07:06,002 - INFO - Torch threads set to 1
2025-04-07 15:07:10,355 - INFO - Loading model...
2025-04-07 15:07:10,356 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:07:10,356 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:07:10,357 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:07:13,561 - INFO - Model loaded successfully
2025-04-07 15:07:13,561 - INFO - Generating embedding...
2025-04-07 15:07:13,594 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:07:13,595 - INFO - Returning successful result
2025-04-07 15:07:13,596 - INFO - Writing result to stdout
2025-04-07 15:07:13,596 - INFO - Result written successfully
2025-04-07 15:07:14,785 - INFO - Starting embedding helper script
2025-04-07 15:07:14,786 - INFO - Reading input from stdin
2025-04-07 15:07:14,786 - INFO - Input data length: 73
2025-04-07 15:07:14,787 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=27
2025-04-07 15:07:14,787 - INFO - Processing text with length: 27 chars
2025-04-07 15:07:14,787 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:07:14,788 - INFO - Text length: 27
2025-04-07 15:07:16,877 - INFO - Torch threads set to 1
2025-04-07 15:07:21,409 - INFO - Loading model...
2025-04-07 15:07:21,409 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:07:21,409 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:07:21,410 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:07:24,772 - INFO - Model loaded successfully
2025-04-07 15:07:24,772 - INFO - Generating embedding...
2025-04-07 15:07:24,792 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:07:24,792 - INFO - Returning successful result
2025-04-07 15:07:24,793 - INFO - Writing result to stdout
2025-04-07 15:07:24,793 - INFO - Result written successfully
2025-04-07 15:13:51,898 - INFO - Starting embedding helper script
2025-04-07 15:13:51,899 - INFO - Reading input from stdin
2025-04-07 15:13:51,899 - INFO - Input data length: 79
2025-04-07 15:13:51,900 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=33
2025-04-07 15:13:51,900 - INFO - Processing text with length: 33 chars
2025-04-07 15:13:51,900 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:13:51,900 - INFO - Text length: 33
2025-04-07 15:13:54,184 - INFO - Torch threads set to 1
2025-04-07 15:13:58,910 - INFO - Loading model...
2025-04-07 15:13:58,910 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:13:58,911 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:13:58,911 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:14:02,044 - INFO - Model loaded successfully
2025-04-07 15:14:02,044 - INFO - Generating embedding...
2025-04-07 15:14:02,115 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:14:02,127 - INFO - Returning successful result
2025-04-07 15:14:02,129 - INFO - Writing result to stdout
2025-04-07 15:14:02,129 - INFO - Result written successfully
2025-04-07 15:14:03,397 - INFO - Starting embedding helper script
2025-04-07 15:14:03,398 - INFO - Reading input from stdin
2025-04-07 15:14:03,398 - INFO - Input data length: 78
2025-04-07 15:14:03,398 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=32
2025-04-07 15:14:03,399 - INFO - Processing text with length: 32 chars
2025-04-07 15:14:03,399 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:14:03,399 - INFO - Text length: 32
2025-04-07 15:14:05,592 - INFO - Torch threads set to 1
2025-04-07 15:14:10,008 - INFO - Loading model...
2025-04-07 15:14:10,009 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:14:10,009 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:14:10,010 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:14:12,790 - INFO - Model loaded successfully
2025-04-07 15:14:12,791 - INFO - Generating embedding...
2025-04-07 15:14:12,815 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:14:12,816 - INFO - Returning successful result
2025-04-07 15:14:12,816 - INFO - Writing result to stdout
2025-04-07 15:14:12,817 - INFO - Result written successfully
2025-04-07 15:14:14,151 - INFO - Starting embedding helper script
2025-04-07 15:14:14,151 - INFO - Reading input from stdin
2025-04-07 15:14:14,152 - INFO - Input data length: 71
2025-04-07 15:14:14,152 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=25
2025-04-07 15:14:14,152 - INFO - Processing text with length: 25 chars
2025-04-07 15:14:14,153 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:14:14,153 - INFO - Text length: 25
2025-04-07 15:14:16,326 - INFO - Torch threads set to 1
2025-04-07 15:14:20,691 - INFO - Loading model...
2025-04-07 15:14:20,692 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:14:20,692 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:14:20,693 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:14:23,792 - INFO - Model loaded successfully
2025-04-07 15:14:23,792 - INFO - Generating embedding...
2025-04-07 15:14:23,811 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:14:23,812 - INFO - Returning successful result
2025-04-07 15:14:23,813 - INFO - Writing result to stdout
2025-04-07 15:14:23,813 - INFO - Result written successfully
2025-04-07 15:14:25,054 - INFO - Starting embedding helper script
2025-04-07 15:14:25,055 - INFO - Reading input from stdin
2025-04-07 15:14:25,055 - INFO - Input data length: 77
2025-04-07 15:14:25,055 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=31
2025-04-07 15:14:25,055 - INFO - Processing text with length: 31 chars
2025-04-07 15:14:25,056 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:14:25,056 - INFO - Text length: 31
2025-04-07 15:14:27,221 - INFO - Torch threads set to 1
2025-04-07 15:14:31,632 - INFO - Loading model...
2025-04-07 15:14:31,632 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:14:31,632 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:14:31,633 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:14:34,759 - INFO - Model loaded successfully
2025-04-07 15:14:34,759 - INFO - Generating embedding...
2025-04-07 15:14:34,792 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:14:34,793 - INFO - Returning successful result
2025-04-07 15:14:34,794 - INFO - Writing result to stdout
2025-04-07 15:14:34,794 - INFO - Result written successfully
2025-04-07 15:14:36,053 - INFO - Starting embedding helper script
2025-04-07 15:14:36,053 - INFO - Reading input from stdin
2025-04-07 15:14:36,054 - INFO - Input data length: 73
2025-04-07 15:14:36,054 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=27
2025-04-07 15:14:36,054 - INFO - Processing text with length: 27 chars
2025-04-07 15:14:36,055 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:14:36,055 - INFO - Text length: 27
2025-04-07 15:14:38,181 - INFO - Torch threads set to 1
2025-04-07 15:14:42,611 - INFO - Loading model...
2025-04-07 15:14:42,611 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:14:42,612 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:14:42,613 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:14:46,715 - INFO - Model loaded successfully
2025-04-07 15:14:46,715 - INFO - Generating embedding...
2025-04-07 15:14:46,735 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:14:46,736 - INFO - Returning successful result
2025-04-07 15:14:46,737 - INFO - Writing result to stdout
2025-04-07 15:14:46,737 - INFO - Result written successfully
2025-04-07 15:16:23,241 - INFO - Starting embedding helper script
2025-04-07 15:16:23,242 - INFO - Reading input from stdin
2025-04-07 15:16:23,242 - INFO - Input data length: 300
2025-04-07 15:16:23,243 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=49
2025-04-07 15:16:23,243 - INFO - Processing text with length: 49 chars
2025-04-07 15:16:23,244 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:16:23,244 - INFO - Text length: 49
2025-04-07 15:16:25,640 - INFO - Torch threads set to 1
2025-04-07 15:16:30,534 - INFO - Loading model...
2025-04-07 15:16:30,534 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:16:30,535 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:16:30,536 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:16:33,507 - INFO - Model loaded successfully
2025-04-07 15:16:33,508 - INFO - Generating embedding...
2025-04-07 15:16:33,532 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:16:33,533 - INFO - Returning successful result
2025-04-07 15:16:33,534 - INFO - Writing result to stdout
2025-04-07 15:16:33,534 - INFO - Result written successfully
2025-04-07 15:28:32,687 - INFO - Starting embedding helper script
2025-04-07 15:28:32,689 - INFO - Reading input from stdin
2025-04-07 15:28:32,689 - INFO - Input data length: 293
2025-04-07 15:28:32,690 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=47
2025-04-07 15:28:32,690 - INFO - Processing text with length: 47 chars
2025-04-07 15:28:32,690 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:28:32,691 - INFO - Text length: 47
2025-04-07 15:28:35,061 - INFO - Torch threads set to 1
2025-04-07 15:28:39,537 - INFO - Loading model...
2025-04-07 15:28:39,537 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:28:39,537 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:28:39,538 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:28:43,172 - INFO - Model loaded successfully
2025-04-07 15:28:43,173 - INFO - Generating embedding...
2025-04-07 15:28:43,198 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:28:43,199 - INFO - Returning successful result
2025-04-07 15:28:43,200 - INFO - Writing result to stdout
2025-04-07 15:28:43,200 - INFO - Result written successfully
2025-04-07 15:39:19,855 - INFO - Starting embedding helper script
2025-04-07 15:39:19,856 - INFO - Reading input from stdin
2025-04-07 15:39:19,857 - INFO - Input data length: 293
2025-04-07 15:39:19,857 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=47
2025-04-07 15:39:19,858 - INFO - Processing text with length: 47 chars
2025-04-07 15:39:19,858 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:39:19,858 - INFO - Text length: 47
2025-04-07 15:39:21,950 - INFO - Torch threads set to 1
2025-04-07 15:39:26,464 - INFO - Loading model...
2025-04-07 15:39:26,464 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:39:26,465 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:39:26,466 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:39:29,806 - INFO - Model loaded successfully
2025-04-07 15:39:29,806 - INFO - Generating embedding...
2025-04-07 15:39:29,840 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:39:29,841 - INFO - Returning successful result
2025-04-07 15:39:29,841 - INFO - Writing result to stdout
2025-04-07 15:39:29,841 - INFO - Result written successfully
2025-04-07 15:44:10,110 - INFO - Starting embedding helper script
2025-04-07 15:44:10,111 - INFO - Reading input from stdin
2025-04-07 15:44:10,111 - INFO - Input data length: 87
2025-04-07 15:44:10,112 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=39
2025-04-07 15:44:10,112 - INFO - Processing text with length: 39 chars
2025-04-07 15:44:10,112 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:44:10,113 - INFO - Text length: 39
2025-04-07 15:44:12,119 - INFO - Torch threads set to 1
2025-04-07 15:44:16,675 - INFO - Loading model...
2025-04-07 15:44:16,675 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:44:16,676 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:44:16,677 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:44:19,927 - INFO - Model loaded successfully
2025-04-07 15:44:19,928 - INFO - Generating embedding...
2025-04-07 15:44:19,952 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:44:19,953 - INFO - Returning successful result
2025-04-07 15:44:19,953 - INFO - Writing result to stdout
2025-04-07 15:44:19,953 - INFO - Result written successfully
2025-04-07 15:45:02,428 - INFO - Starting embedding helper script
2025-04-07 15:45:02,429 - INFO - Reading input from stdin
2025-04-07 15:45:02,429 - INFO - Input data length: 99
2025-04-07 15:45:02,429 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=53
2025-04-07 15:45:02,430 - INFO - Processing text with length: 53 chars
2025-04-07 15:45:02,430 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:45:02,430 - INFO - Text length: 53
2025-04-07 15:45:04,448 - INFO - Torch threads set to 1
2025-04-07 15:45:09,155 - INFO - Loading model...
2025-04-07 15:45:09,157 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:45:09,157 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:45:09,158 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:45:11,949 - INFO - Model loaded successfully
2025-04-07 15:45:11,949 - INFO - Generating embedding...
2025-04-07 15:45:11,974 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:45:11,975 - INFO - Returning successful result
2025-04-07 15:45:11,975 - INFO - Writing result to stdout
2025-04-07 15:45:11,975 - INFO - Result written successfully
2025-04-07 15:45:43,271 - INFO - Starting embedding helper script
2025-04-07 15:45:43,271 - INFO - Reading input from stdin
2025-04-07 15:45:43,272 - INFO - Input data length: 79
2025-04-07 15:45:43,272 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=33
2025-04-07 15:45:43,272 - INFO - Processing text with length: 33 chars
2025-04-07 15:45:43,273 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:45:43,273 - INFO - Text length: 33
2025-04-07 15:45:45,554 - INFO - Torch threads set to 1
2025-04-07 15:45:50,162 - INFO - Loading model...
2025-04-07 15:45:50,163 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:45:50,163 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:45:50,164 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:45:53,475 - INFO - Model loaded successfully
2025-04-07 15:45:53,476 - INFO - Generating embedding...
2025-04-07 15:45:53,495 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:45:53,496 - INFO - Returning successful result
2025-04-07 15:45:53,496 - INFO - Writing result to stdout
2025-04-07 15:45:53,497 - INFO - Result written successfully
2025-04-07 15:45:54,811 - INFO - Starting embedding helper script
2025-04-07 15:45:54,811 - INFO - Reading input from stdin
2025-04-07 15:45:54,812 - INFO - Input data length: 78
2025-04-07 15:45:54,812 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=32
2025-04-07 15:45:54,812 - INFO - Processing text with length: 32 chars
2025-04-07 15:45:54,813 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:45:54,813 - INFO - Text length: 32
2025-04-07 15:45:57,492 - INFO - Torch threads set to 1
2025-04-07 15:46:01,974 - INFO - Loading model...
2025-04-07 15:46:01,974 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:46:01,974 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:46:01,975 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:46:05,299 - INFO - Model loaded successfully
2025-04-07 15:46:05,299 - INFO - Generating embedding...
2025-04-07 15:46:05,320 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:46:05,321 - INFO - Returning successful result
2025-04-07 15:46:05,321 - INFO - Writing result to stdout
2025-04-07 15:46:05,322 - INFO - Result written successfully
2025-04-07 15:46:06,578 - INFO - Starting embedding helper script
2025-04-07 15:46:06,578 - INFO - Reading input from stdin
2025-04-07 15:46:06,579 - INFO - Input data length: 71
2025-04-07 15:46:06,579 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=25
2025-04-07 15:46:06,579 - INFO - Processing text with length: 25 chars
2025-04-07 15:46:06,580 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:46:06,580 - INFO - Text length: 25
2025-04-07 15:46:08,752 - INFO - Torch threads set to 1
2025-04-07 15:46:13,149 - INFO - Loading model...
2025-04-07 15:46:13,149 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:46:13,149 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:46:13,150 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:46:16,129 - INFO - Model loaded successfully
2025-04-07 15:46:16,130 - INFO - Generating embedding...
2025-04-07 15:46:16,194 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:46:16,195 - INFO - Returning successful result
2025-04-07 15:46:16,196 - INFO - Writing result to stdout
2025-04-07 15:46:16,196 - INFO - Result written successfully
2025-04-07 15:46:17,411 - INFO - Starting embedding helper script
2025-04-07 15:46:17,412 - INFO - Reading input from stdin
2025-04-07 15:46:17,412 - INFO - Input data length: 77
2025-04-07 15:46:17,412 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=31
2025-04-07 15:46:17,413 - INFO - Processing text with length: 31 chars
2025-04-07 15:46:17,413 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:46:17,414 - INFO - Text length: 31
2025-04-07 15:46:19,659 - INFO - Torch threads set to 1
2025-04-07 15:46:24,018 - INFO - Loading model...
2025-04-07 15:46:24,018 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:46:24,019 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:46:24,020 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:46:26,999 - INFO - Model loaded successfully
2025-04-07 15:46:26,999 - INFO - Generating embedding...
2025-04-07 15:46:27,020 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:46:27,021 - INFO - Returning successful result
2025-04-07 15:46:27,021 - INFO - Writing result to stdout
2025-04-07 15:46:27,022 - INFO - Result written successfully
2025-04-07 15:46:28,323 - INFO - Starting embedding helper script
2025-04-07 15:46:28,324 - INFO - Reading input from stdin
2025-04-07 15:46:28,324 - INFO - Input data length: 73
2025-04-07 15:46:28,324 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=27
2025-04-07 15:46:28,325 - INFO - Processing text with length: 27 chars
2025-04-07 15:46:28,325 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-07 15:46:28,325 - INFO - Text length: 27
2025-04-07 15:46:30,452 - INFO - Torch threads set to 1
2025-04-07 15:46:34,823 - INFO - Loading model...
2025-04-07 15:46:34,824 - INFO - Added fallback init_empty_weights implementation
2025-04-07 15:46:34,824 - INFO - Added fallback find_tied_parameters implementation
2025-04-07 15:46:34,825 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-07 15:46:37,854 - INFO - Model loaded successfully
2025-04-07 15:46:37,854 - INFO - Generating embedding...
2025-04-07 15:46:37,875 - INFO - Embedding generated successfully with shape: (384,)
2025-04-07 15:46:37,876 - INFO - Returning successful result
2025-04-07 15:46:37,876 - INFO - Writing result to stdout
2025-04-07 15:46:37,876 - INFO - Result written successfully
2025-04-08 07:58:26,001 - INFO - Starting embedding helper script
2025-04-08 07:58:26,002 - INFO - Reading input from stdin
2025-04-08 07:58:26,002 - INFO - Input data length: 363
2025-04-08 07:58:26,003 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=62
2025-04-08 07:58:26,003 - INFO - Processing text with length: 62 chars
2025-04-08 07:58:26,003 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 07:58:26,004 - INFO - Text length: 62
2025-04-08 07:58:30,715 - INFO - Torch threads set to 1
2025-04-08 07:58:37,751 - INFO - Loading model...
2025-04-08 07:58:37,752 - INFO - Added fallback init_empty_weights implementation
2025-04-08 07:58:37,753 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 07:58:37,754 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 07:58:41,214 - INFO - Model loaded successfully
2025-04-08 07:58:41,215 - INFO - Generating embedding...
2025-04-08 07:58:41,522 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 07:58:41,524 - INFO - Returning successful result
2025-04-08 07:58:41,524 - INFO - Writing result to stdout
2025-04-08 07:58:41,525 - INFO - Result written successfully
2025-04-08 07:59:50,545 - INFO - Starting embedding helper script
2025-04-08 07:59:50,546 - INFO - Reading input from stdin
2025-04-08 07:59:50,546 - INFO - Input data length: 417
2025-04-08 07:59:50,547 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=76
2025-04-08 07:59:50,547 - INFO - Processing text with length: 76 chars
2025-04-08 07:59:50,548 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 07:59:50,548 - INFO - Text length: 76
2025-04-08 07:59:54,335 - INFO - Torch threads set to 1
2025-04-08 08:00:00,182 - INFO - Loading model...
2025-04-08 08:00:00,183 - INFO - Added fallback init_empty_weights implementation
2025-04-08 08:00:00,183 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 08:00:00,185 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 08:00:05,561 - INFO - Model loaded successfully
2025-04-08 08:00:05,561 - INFO - Generating embedding...
2025-04-08 08:00:05,599 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 08:00:05,600 - INFO - Returning successful result
2025-04-08 08:00:05,601 - INFO - Writing result to stdout
2025-04-08 08:00:05,601 - INFO - Result written successfully
2025-04-08 08:04:57,629 - INFO - Starting embedding helper script
2025-04-08 08:04:57,629 - INFO - Reading input from stdin
2025-04-08 08:04:57,630 - INFO - Input data length: 72
2025-04-08 08:04:57,630 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=26
2025-04-08 08:04:57,630 - INFO - Processing text with length: 26 chars
2025-04-08 08:04:57,631 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 08:04:57,631 - INFO - Text length: 26
2025-04-08 08:05:00,687 - INFO - Torch threads set to 1
2025-04-08 08:05:06,782 - INFO - Loading model...
2025-04-08 08:05:06,782 - INFO - Added fallback init_empty_weights implementation
2025-04-08 08:05:06,783 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 08:05:06,784 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 08:05:10,134 - INFO - Model loaded successfully
2025-04-08 08:05:10,135 - INFO - Generating embedding...
2025-04-08 08:05:10,158 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 08:05:10,159 - INFO - Returning successful result
2025-04-08 08:05:10,159 - INFO - Writing result to stdout
2025-04-08 08:05:10,159 - INFO - Result written successfully
2025-04-08 08:05:11,479 - INFO - Starting embedding helper script
2025-04-08 08:05:11,479 - INFO - Reading input from stdin
2025-04-08 08:05:11,480 - INFO - Input data length: 80
2025-04-08 08:05:11,480 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=34
2025-04-08 08:05:11,480 - INFO - Processing text with length: 34 chars
2025-04-08 08:05:11,481 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 08:05:11,481 - INFO - Text length: 34
2025-04-08 08:05:14,027 - INFO - Torch threads set to 1
2025-04-08 08:05:18,737 - INFO - Loading model...
2025-04-08 08:05:18,737 - INFO - Added fallback init_empty_weights implementation
2025-04-08 08:05:18,737 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 08:05:18,738 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 08:05:21,504 - INFO - Model loaded successfully
2025-04-08 08:05:21,504 - INFO - Generating embedding...
2025-04-08 08:05:21,530 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 08:05:21,531 - INFO - Returning successful result
2025-04-08 08:05:21,531 - INFO - Writing result to stdout
2025-04-08 08:05:21,531 - INFO - Result written successfully
2025-04-08 08:05:22,708 - INFO - Starting embedding helper script
2025-04-08 08:05:22,709 - INFO - Reading input from stdin
2025-04-08 08:05:22,709 - INFO - Input data length: 69
2025-04-08 08:05:22,709 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=23
2025-04-08 08:05:22,710 - INFO - Processing text with length: 23 chars
2025-04-08 08:05:22,710 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 08:05:22,710 - INFO - Text length: 23
2025-04-08 08:05:25,052 - INFO - Torch threads set to 1
2025-04-08 08:05:29,667 - INFO - Loading model...
2025-04-08 08:05:29,668 - INFO - Added fallback init_empty_weights implementation
2025-04-08 08:05:29,668 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 08:05:29,670 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 08:05:32,573 - INFO - Model loaded successfully
2025-04-08 08:05:32,573 - INFO - Generating embedding...
2025-04-08 08:05:32,598 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 08:05:32,599 - INFO - Returning successful result
2025-04-08 08:05:32,599 - INFO - Writing result to stdout
2025-04-08 08:05:32,600 - INFO - Result written successfully
2025-04-08 08:05:33,818 - INFO - Starting embedding helper script
2025-04-08 08:05:33,818 - INFO - Reading input from stdin
2025-04-08 08:05:33,819 - INFO - Input data length: 72
2025-04-08 08:05:33,819 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=26
2025-04-08 08:05:33,820 - INFO - Processing text with length: 26 chars
2025-04-08 08:05:33,820 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 08:05:33,820 - INFO - Text length: 26
2025-04-08 08:05:36,350 - INFO - Torch threads set to 1
2025-04-08 08:05:40,835 - INFO - Loading model...
2025-04-08 08:05:40,836 - INFO - Added fallback init_empty_weights implementation
2025-04-08 08:05:40,836 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 08:05:40,837 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 08:05:43,429 - INFO - Model loaded successfully
2025-04-08 08:05:43,430 - INFO - Generating embedding...
2025-04-08 08:05:43,453 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 08:05:43,455 - INFO - Returning successful result
2025-04-08 08:05:43,455 - INFO - Writing result to stdout
2025-04-08 08:05:43,455 - INFO - Result written successfully
2025-04-08 08:05:44,733 - INFO - Starting embedding helper script
2025-04-08 08:05:44,733 - INFO - Reading input from stdin
2025-04-08 08:05:44,733 - INFO - Input data length: 76
2025-04-08 08:05:44,734 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=30
2025-04-08 08:05:44,734 - INFO - Processing text with length: 30 chars
2025-04-08 08:05:44,734 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 08:05:44,735 - INFO - Text length: 30
2025-04-08 08:05:47,127 - INFO - Torch threads set to 1
2025-04-08 08:05:51,580 - INFO - Loading model...
2025-04-08 08:05:51,581 - INFO - Added fallback init_empty_weights implementation
2025-04-08 08:05:51,581 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 08:05:51,582 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 08:05:54,243 - INFO - Model loaded successfully
2025-04-08 08:05:54,244 - INFO - Generating embedding...
2025-04-08 08:05:54,266 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 08:05:54,267 - INFO - Returning successful result
2025-04-08 08:05:54,267 - INFO - Writing result to stdout
2025-04-08 08:05:54,267 - INFO - Result written successfully
2025-04-08 08:05:55,586 - INFO - Starting embedding helper script
2025-04-08 08:05:55,587 - INFO - Reading input from stdin
2025-04-08 08:05:55,587 - INFO - Input data length: 144
2025-04-08 08:05:55,587 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=18
2025-04-08 08:05:55,588 - INFO - Processing text with length: 18 chars
2025-04-08 08:05:55,588 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 08:05:55,588 - INFO - Text length: 18
2025-04-08 08:05:57,982 - INFO - Torch threads set to 1
2025-04-08 08:06:02,345 - INFO - Loading model...
2025-04-08 08:06:02,345 - INFO - Added fallback init_empty_weights implementation
2025-04-08 08:06:02,345 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 08:06:02,346 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 08:06:05,568 - INFO - Model loaded successfully
2025-04-08 08:06:05,569 - INFO - Generating embedding...
2025-04-08 08:06:05,595 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 08:06:05,596 - INFO - Returning successful result
2025-04-08 08:06:05,597 - INFO - Writing result to stdout
2025-04-08 08:06:05,597 - INFO - Result written successfully
2025-04-08 08:06:07,389 - INFO - Starting embedding helper script
2025-04-08 08:06:07,389 - INFO - Reading input from stdin
2025-04-08 08:06:07,390 - INFO - Input data length: 144
2025-04-08 08:06:07,390 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=18
2025-04-08 08:06:07,390 - INFO - Processing text with length: 18 chars
2025-04-08 08:06:07,391 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 08:06:07,391 - INFO - Text length: 18
2025-04-08 08:06:10,358 - INFO - Torch threads set to 1
2025-04-08 08:06:15,876 - INFO - Loading model...
2025-04-08 08:06:15,876 - INFO - Added fallback init_empty_weights implementation
2025-04-08 08:06:15,876 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 08:06:15,877 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 08:06:19,084 - INFO - Model loaded successfully
2025-04-08 08:06:19,084 - INFO - Generating embedding...
2025-04-08 08:06:19,105 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 08:06:19,106 - INFO - Returning successful result
2025-04-08 08:06:19,106 - INFO - Writing result to stdout
2025-04-08 08:06:19,107 - INFO - Result written successfully
2025-04-08 08:06:20,533 - INFO - Starting embedding helper script
2025-04-08 08:06:20,533 - INFO - Reading input from stdin
2025-04-08 08:06:20,534 - INFO - Input data length: 162
2025-04-08 08:06:20,534 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=21
2025-04-08 08:06:20,534 - INFO - Processing text with length: 21 chars
2025-04-08 08:06:20,535 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 08:06:20,535 - INFO - Text length: 21
2025-04-08 08:06:23,180 - INFO - Torch threads set to 1
2025-04-08 08:06:30,999 - INFO - Loading model...
2025-04-08 08:06:31,000 - INFO - Added fallback init_empty_weights implementation
2025-04-08 08:06:31,000 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 08:06:31,001 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 08:06:34,358 - INFO - Model loaded successfully
2025-04-08 08:06:34,359 - INFO - Generating embedding...
2025-04-08 08:06:34,385 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 08:06:34,387 - INFO - Returning successful result
2025-04-08 08:06:34,387 - INFO - Writing result to stdout
2025-04-08 08:06:34,387 - INFO - Result written successfully
2025-04-08 09:28:40,786 - INFO - Starting embedding helper script
2025-04-08 09:28:40,787 - INFO - Reading input from stdin
2025-04-08 09:28:40,787 - INFO - Input data length: 69
2025-04-08 09:28:40,788 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=23
2025-04-08 09:28:40,788 - INFO - Processing text with length: 23 chars
2025-04-08 09:28:40,788 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:28:40,789 - INFO - Text length: 23
2025-04-08 09:28:43,342 - INFO - Torch threads set to 1
2025-04-08 09:28:48,221 - INFO - Loading model...
2025-04-08 09:28:48,222 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:28:48,222 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:28:48,223 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:28:51,505 - INFO - Model loaded successfully
2025-04-08 09:28:51,506 - INFO - Generating embedding...
2025-04-08 09:28:53,157 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:28:53,158 - INFO - Returning successful result
2025-04-08 09:28:53,159 - INFO - Writing result to stdout
2025-04-08 09:28:53,160 - INFO - Result written successfully
2025-04-08 09:28:54,669 - INFO - Starting embedding helper script
2025-04-08 09:28:54,670 - INFO - Reading input from stdin
2025-04-08 09:28:54,670 - INFO - Input data length: 73
2025-04-08 09:28:54,671 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=27
2025-04-08 09:28:54,671 - INFO - Processing text with length: 27 chars
2025-04-08 09:28:54,671 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:28:54,672 - INFO - Text length: 27
2025-04-08 09:28:57,888 - INFO - Torch threads set to 1
2025-04-08 09:29:02,775 - INFO - Loading model...
2025-04-08 09:29:02,776 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:29:02,776 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:29:02,777 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:29:05,856 - INFO - Model loaded successfully
2025-04-08 09:29:05,856 - INFO - Generating embedding...
2025-04-08 09:29:05,892 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:29:05,893 - INFO - Returning successful result
2025-04-08 09:29:05,893 - INFO - Writing result to stdout
2025-04-08 09:29:05,894 - INFO - Result written successfully
2025-04-08 09:29:07,133 - INFO - Starting embedding helper script
2025-04-08 09:29:07,133 - INFO - Reading input from stdin
2025-04-08 09:29:07,134 - INFO - Input data length: 75
2025-04-08 09:29:07,134 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=29
2025-04-08 09:29:07,134 - INFO - Processing text with length: 29 chars
2025-04-08 09:29:07,135 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:29:07,135 - INFO - Text length: 29
2025-04-08 09:29:09,409 - INFO - Torch threads set to 1
2025-04-08 09:29:14,265 - INFO - Loading model...
2025-04-08 09:29:14,265 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:29:14,265 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:29:14,266 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:29:17,181 - INFO - Model loaded successfully
2025-04-08 09:29:17,182 - INFO - Generating embedding...
2025-04-08 09:29:17,206 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:29:17,207 - INFO - Returning successful result
2025-04-08 09:29:17,207 - INFO - Writing result to stdout
2025-04-08 09:29:17,208 - INFO - Result written successfully
2025-04-08 09:29:18,650 - INFO - Starting embedding helper script
2025-04-08 09:29:18,650 - INFO - Reading input from stdin
2025-04-08 09:29:18,651 - INFO - Input data length: 79
2025-04-08 09:29:18,651 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=33
2025-04-08 09:29:18,652 - INFO - Processing text with length: 33 chars
2025-04-08 09:29:18,652 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:29:18,652 - INFO - Text length: 33
2025-04-08 09:29:21,286 - INFO - Torch threads set to 1
2025-04-08 09:29:26,418 - INFO - Loading model...
2025-04-08 09:29:26,418 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:29:26,419 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:29:26,420 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:29:29,607 - INFO - Model loaded successfully
2025-04-08 09:29:29,607 - INFO - Generating embedding...
2025-04-08 09:29:29,632 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:29:29,633 - INFO - Returning successful result
2025-04-08 09:29:29,633 - INFO - Writing result to stdout
2025-04-08 09:29:29,634 - INFO - Result written successfully
2025-04-08 09:29:31,525 - INFO - Starting embedding helper script
2025-04-08 09:29:31,526 - INFO - Reading input from stdin
2025-04-08 09:29:31,526 - INFO - Input data length: 77
2025-04-08 09:29:31,527 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=31
2025-04-08 09:29:31,528 - INFO - Processing text with length: 31 chars
2025-04-08 09:29:31,528 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:29:31,529 - INFO - Text length: 31
2025-04-08 09:29:34,596 - INFO - Torch threads set to 1
2025-04-08 09:29:39,431 - INFO - Loading model...
2025-04-08 09:29:39,431 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:29:39,431 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:29:39,432 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:29:42,369 - INFO - Model loaded successfully
2025-04-08 09:29:42,369 - INFO - Generating embedding...
2025-04-08 09:29:42,403 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:29:42,404 - INFO - Returning successful result
2025-04-08 09:29:42,404 - INFO - Writing result to stdout
2025-04-08 09:29:42,405 - INFO - Result written successfully
2025-04-08 09:29:43,789 - INFO - Starting embedding helper script
2025-04-08 09:29:43,789 - INFO - Reading input from stdin
2025-04-08 09:29:43,790 - INFO - Input data length: 144
2025-04-08 09:29:43,790 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=18
2025-04-08 09:29:43,791 - INFO - Processing text with length: 18 chars
2025-04-08 09:29:43,791 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:29:43,791 - INFO - Text length: 18
2025-04-08 09:29:46,175 - INFO - Torch threads set to 1
2025-04-08 09:29:50,902 - INFO - Loading model...
2025-04-08 09:29:50,902 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:29:50,903 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:29:50,904 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:29:53,820 - INFO - Model loaded successfully
2025-04-08 09:29:53,821 - INFO - Generating embedding...
2025-04-08 09:29:53,853 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:29:53,854 - INFO - Returning successful result
2025-04-08 09:29:53,854 - INFO - Writing result to stdout
2025-04-08 09:29:53,854 - INFO - Result written successfully
2025-04-08 09:29:55,241 - INFO - Starting embedding helper script
2025-04-08 09:29:55,241 - INFO - Reading input from stdin
2025-04-08 09:29:55,241 - INFO - Input data length: 132
2025-04-08 09:29:55,242 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=16
2025-04-08 09:29:55,242 - INFO - Processing text with length: 16 chars
2025-04-08 09:29:55,243 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:29:55,243 - INFO - Text length: 16
2025-04-08 09:29:57,394 - INFO - Torch threads set to 1
2025-04-08 09:30:02,080 - INFO - Loading model...
2025-04-08 09:30:02,081 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:30:02,081 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:30:02,082 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:30:05,590 - INFO - Model loaded successfully
2025-04-08 09:30:05,590 - INFO - Generating embedding...
2025-04-08 09:30:05,615 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:30:05,617 - INFO - Returning successful result
2025-04-08 09:30:05,617 - INFO - Writing result to stdout
2025-04-08 09:30:05,617 - INFO - Result written successfully
2025-04-08 09:30:06,828 - INFO - Starting embedding helper script
2025-04-08 09:30:06,829 - INFO - Reading input from stdin
2025-04-08 09:30:06,829 - INFO - Input data length: 138
2025-04-08 09:30:06,829 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=17
2025-04-08 09:30:06,830 - INFO - Processing text with length: 17 chars
2025-04-08 09:30:06,830 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:30:06,830 - INFO - Text length: 17
2025-04-08 09:30:08,946 - INFO - Torch threads set to 1
2025-04-08 09:30:15,078 - INFO - Loading model...
2025-04-08 09:30:15,078 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:30:15,079 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:30:15,080 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:30:26,492 - INFO - Model loaded successfully
2025-04-08 09:30:26,493 - INFO - Generating embedding...
2025-04-08 09:30:26,522 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:30:26,523 - INFO - Returning successful result
2025-04-08 09:30:26,523 - INFO - Writing result to stdout
2025-04-08 09:30:26,523 - INFO - Result written successfully
2025-04-08 09:38:05,123 - INFO - Starting embedding helper script
2025-04-08 09:38:05,124 - INFO - Reading input from stdin
2025-04-08 09:38:05,124 - INFO - Input data length: 79
2025-04-08 09:38:05,125 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=33
2025-04-08 09:38:05,125 - INFO - Processing text with length: 33 chars
2025-04-08 09:38:05,125 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:38:05,126 - INFO - Text length: 33
2025-04-08 09:38:08,086 - INFO - Torch threads set to 1
2025-04-08 09:38:13,038 - INFO - Loading model...
2025-04-08 09:38:13,038 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:38:13,038 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:38:13,039 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:38:15,991 - INFO - Model loaded successfully
2025-04-08 09:38:15,992 - INFO - Generating embedding...
2025-04-08 09:38:16,032 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:38:16,033 - INFO - Returning successful result
2025-04-08 09:38:16,033 - INFO - Writing result to stdout
2025-04-08 09:38:16,034 - INFO - Result written successfully
2025-04-08 09:38:17,342 - INFO - Starting embedding helper script
2025-04-08 09:38:17,342 - INFO - Reading input from stdin
2025-04-08 09:38:17,343 - INFO - Input data length: 78
2025-04-08 09:38:17,343 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=32
2025-04-08 09:38:17,343 - INFO - Processing text with length: 32 chars
2025-04-08 09:38:17,344 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:38:17,344 - INFO - Text length: 32
2025-04-08 09:38:19,783 - INFO - Torch threads set to 1
2025-04-08 09:38:24,456 - INFO - Loading model...
2025-04-08 09:38:24,456 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:38:24,457 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:38:24,457 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:38:27,345 - INFO - Model loaded successfully
2025-04-08 09:38:27,345 - INFO - Generating embedding...
2025-04-08 09:38:27,370 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:38:27,371 - INFO - Returning successful result
2025-04-08 09:38:27,371 - INFO - Writing result to stdout
2025-04-08 09:38:27,372 - INFO - Result written successfully
2025-04-08 09:38:28,636 - INFO - Starting embedding helper script
2025-04-08 09:38:28,637 - INFO - Reading input from stdin
2025-04-08 09:38:28,637 - INFO - Input data length: 71
2025-04-08 09:38:28,637 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=25
2025-04-08 09:38:28,638 - INFO - Processing text with length: 25 chars
2025-04-08 09:38:28,638 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:38:28,639 - INFO - Text length: 25
2025-04-08 09:38:30,850 - INFO - Torch threads set to 1
2025-04-08 09:38:35,383 - INFO - Loading model...
2025-04-08 09:38:35,384 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:38:35,384 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:38:35,385 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:38:38,646 - INFO - Model loaded successfully
2025-04-08 09:38:38,646 - INFO - Generating embedding...
2025-04-08 09:38:38,669 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:38:38,670 - INFO - Returning successful result
2025-04-08 09:38:38,670 - INFO - Writing result to stdout
2025-04-08 09:38:38,670 - INFO - Result written successfully
2025-04-08 09:38:39,967 - INFO - Starting embedding helper script
2025-04-08 09:38:39,968 - INFO - Reading input from stdin
2025-04-08 09:38:39,968 - INFO - Input data length: 77
2025-04-08 09:38:39,969 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=31
2025-04-08 09:38:39,969 - INFO - Processing text with length: 31 chars
2025-04-08 09:38:39,969 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:38:39,970 - INFO - Text length: 31
2025-04-08 09:38:42,332 - INFO - Torch threads set to 1
2025-04-08 09:38:46,821 - INFO - Loading model...
2025-04-08 09:38:46,821 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:38:46,821 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:38:46,822 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:38:50,078 - INFO - Model loaded successfully
2025-04-08 09:38:50,079 - INFO - Generating embedding...
2025-04-08 09:38:50,102 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:38:50,103 - INFO - Returning successful result
2025-04-08 09:38:50,104 - INFO - Writing result to stdout
2025-04-08 09:38:50,104 - INFO - Result written successfully
2025-04-08 09:38:51,401 - INFO - Starting embedding helper script
2025-04-08 09:38:51,401 - INFO - Reading input from stdin
2025-04-08 09:38:51,401 - INFO - Input data length: 73
2025-04-08 09:38:51,402 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=27
2025-04-08 09:38:51,402 - INFO - Processing text with length: 27 chars
2025-04-08 09:38:51,402 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-08 09:38:51,403 - INFO - Text length: 27
2025-04-08 09:38:53,726 - INFO - Torch threads set to 1
2025-04-08 09:38:58,152 - INFO - Loading model...
2025-04-08 09:38:58,152 - INFO - Added fallback init_empty_weights implementation
2025-04-08 09:38:58,152 - INFO - Added fallback find_tied_parameters implementation
2025-04-08 09:38:58,153 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-08 09:39:01,017 - INFO - Model loaded successfully
2025-04-08 09:39:01,017 - INFO - Generating embedding...
2025-04-08 09:39:01,067 - INFO - Embedding generated successfully with shape: (384,)
2025-04-08 09:39:01,082 - INFO - Returning successful result
2025-04-08 09:39:01,082 - INFO - Writing result to stdout
2025-04-08 09:39:01,082 - INFO - Result written successfully
2025-04-12 07:11:24,009 - INFO - Starting embedding helper script
2025-04-12 07:11:24,015 - INFO - Reading input from stdin
2025-04-12 07:11:24,016 - INFO - Input data length: 2013
2025-04-12 07:11:24,016 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1967
2025-04-12 07:11:24,016 - INFO - Processing text with length: 1967 chars
2025-04-12 07:11:24,017 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 07:11:24,017 - INFO - Text length: 1967
2025-04-12 07:11:30,207 - INFO - Torch threads set to 1
2025-04-12 07:11:40,367 - INFO - Loading model...
2025-04-12 07:11:40,369 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 07:11:46,741 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-04-12 07:12:04,524 - INFO - Model loaded successfully
2025-04-12 07:12:04,525 - INFO - Generating embedding...
2025-04-12 07:12:04,984 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 07:12:04,986 - INFO - Returning successful result
2025-04-12 07:12:04,986 - INFO - Writing result to stdout
2025-04-12 07:12:04,986 - INFO - Result written successfully
2025-04-12 07:12:07,883 - INFO - Starting embedding helper script
2025-04-12 07:12:07,884 - INFO - Reading input from stdin
2025-04-12 07:12:07,889 - INFO - Input data length: 599
2025-04-12 07:12:07,889 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=553
2025-04-12 07:12:07,891 - INFO - Processing text with length: 553 chars
2025-04-12 07:12:07,893 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 07:12:07,894 - INFO - Text length: 553
2025-04-12 07:12:13,896 - INFO - Torch threads set to 1
2025-04-12 07:12:23,660 - INFO - Loading model...
2025-04-12 07:12:23,661 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 07:12:28,415 - INFO - Model loaded successfully
2025-04-12 07:12:28,415 - INFO - Generating embedding...
2025-04-12 07:12:28,603 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 07:12:28,603 - INFO - Returning successful result
2025-04-12 07:12:28,604 - INFO - Writing result to stdout
2025-04-12 07:12:28,604 - INFO - Result written successfully
2025-04-12 07:12:30,714 - INFO - Starting embedding helper script
2025-04-12 07:12:30,714 - INFO - Reading input from stdin
2025-04-12 07:12:30,714 - INFO - Input data length: 2025
2025-04-12 07:12:30,714 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1979
2025-04-12 07:12:30,715 - INFO - Processing text with length: 1979 chars
2025-04-12 07:12:30,715 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 07:12:30,715 - INFO - Text length: 1979
2025-04-12 07:12:34,480 - INFO - Torch threads set to 1
2025-04-12 07:12:42,567 - INFO - Loading model...
2025-04-12 07:12:42,571 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 07:12:47,566 - INFO - Model loaded successfully
2025-04-12 07:12:47,566 - INFO - Generating embedding...
2025-04-12 07:12:48,015 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 07:12:48,019 - INFO - Returning successful result
2025-04-12 07:12:48,024 - INFO - Writing result to stdout
2025-04-12 07:12:48,024 - INFO - Result written successfully
2025-04-12 07:12:50,009 - INFO - Starting embedding helper script
2025-04-12 07:12:50,009 - INFO - Reading input from stdin
2025-04-12 07:12:50,009 - INFO - Input data length: 1009
2025-04-12 07:12:50,010 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=963
2025-04-12 07:12:50,010 - INFO - Processing text with length: 963 chars
2025-04-12 07:12:50,010 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 07:12:50,010 - INFO - Text length: 963
2025-04-12 07:12:55,910 - INFO - Torch threads set to 1
2025-04-12 07:13:04,606 - INFO - Loading model...
2025-04-12 07:13:04,611 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 07:13:09,484 - INFO - Model loaded successfully
2025-04-12 07:13:09,485 - INFO - Generating embedding...
2025-04-12 07:13:09,607 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 07:13:09,608 - INFO - Returning successful result
2025-04-12 07:13:09,609 - INFO - Writing result to stdout
2025-04-12 07:13:09,610 - INFO - Result written successfully
2025-04-12 07:13:53,736 - INFO - Starting embedding helper script
2025-04-12 07:13:53,737 - INFO - Reading input from stdin
2025-04-12 07:13:53,737 - INFO - Input data length: 2036
2025-04-12 07:13:53,737 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1990
2025-04-12 07:13:53,738 - INFO - Processing text with length: 1990 chars
2025-04-12 07:13:53,738 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 07:13:53,739 - INFO - Text length: 1990
2025-04-12 07:13:58,397 - INFO - Torch threads set to 1
2025-04-12 07:14:06,795 - INFO - Loading model...
2025-04-12 07:14:06,798 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 07:14:11,733 - INFO - Model loaded successfully
2025-04-12 07:14:11,733 - INFO - Generating embedding...
2025-04-12 07:14:12,037 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 07:14:12,040 - INFO - Returning successful result
2025-04-12 07:14:12,049 - INFO - Writing result to stdout
2025-04-12 07:14:12,050 - INFO - Result written successfully
2025-04-12 07:14:14,556 - INFO - Starting embedding helper script
2025-04-12 07:14:14,556 - INFO - Reading input from stdin
2025-04-12 07:14:14,557 - INFO - Input data length: 1732
2025-04-12 07:14:14,557 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1686
2025-04-12 07:14:14,557 - INFO - Processing text with length: 1686 chars
2025-04-12 07:14:14,558 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 07:14:14,558 - INFO - Text length: 1686
2025-04-12 07:14:19,286 - INFO - Torch threads set to 1
2025-04-12 07:14:29,539 - INFO - Loading model...
2025-04-12 07:14:29,541 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 07:14:35,256 - INFO - Model loaded successfully
2025-04-12 07:14:35,256 - INFO - Generating embedding...
2025-04-12 07:14:35,434 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 07:14:35,436 - INFO - Returning successful result
2025-04-12 07:14:35,436 - INFO - Writing result to stdout
2025-04-12 07:14:35,436 - INFO - Result written successfully
2025-04-12 07:14:37,500 - INFO - Starting embedding helper script
2025-04-12 07:14:37,501 - INFO - Reading input from stdin
2025-04-12 07:14:37,501 - INFO - Input data length: 2045
2025-04-12 07:14:37,501 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1999
2025-04-12 07:14:37,502 - INFO - Processing text with length: 1999 chars
2025-04-12 07:14:37,502 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 07:14:37,502 - INFO - Text length: 1999
2025-04-12 07:14:42,121 - INFO - Torch threads set to 1
2025-04-12 07:14:51,591 - INFO - Loading model...
2025-04-12 07:14:51,593 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 07:14:57,281 - INFO - Model loaded successfully
2025-04-12 07:14:57,281 - INFO - Generating embedding...
2025-04-12 07:14:57,488 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 07:14:57,503 - INFO - Returning successful result
2025-04-12 07:14:57,521 - INFO - Writing result to stdout
2025-04-12 07:14:57,532 - INFO - Result written successfully
2025-04-12 07:15:00,276 - INFO - Starting embedding helper script
2025-04-12 07:15:00,278 - INFO - Reading input from stdin
2025-04-12 07:15:00,278 - INFO - Input data length: 2044
2025-04-12 07:15:00,280 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1998
2025-04-12 07:15:00,280 - INFO - Processing text with length: 1998 chars
2025-04-12 07:15:00,280 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 07:15:00,280 - INFO - Text length: 1998
2025-04-12 07:15:05,233 - INFO - Torch threads set to 1
2025-04-12 07:15:14,712 - INFO - Loading model...
2025-04-12 07:15:14,714 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 07:15:19,760 - INFO - Model loaded successfully
2025-04-12 07:15:19,761 - INFO - Generating embedding...
2025-04-12 07:15:20,129 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 07:15:20,136 - INFO - Returning successful result
2025-04-12 07:15:20,141 - INFO - Writing result to stdout
2025-04-12 07:15:20,144 - INFO - Result written successfully
2025-04-12 07:15:22,422 - INFO - Starting embedding helper script
2025-04-12 07:15:22,422 - INFO - Reading input from stdin
2025-04-12 07:15:22,423 - INFO - Input data length: 867
2025-04-12 07:15:22,423 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=821
2025-04-12 07:15:22,424 - INFO - Processing text with length: 821 chars
2025-04-12 07:15:22,424 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 07:15:22,424 - INFO - Text length: 821
2025-04-12 07:15:27,095 - INFO - Torch threads set to 1
2025-04-12 07:15:38,276 - INFO - Loading model...
2025-04-12 07:15:38,277 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 07:15:43,442 - INFO - Model loaded successfully
2025-04-12 07:15:43,443 - INFO - Generating embedding...
2025-04-12 07:15:43,620 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 07:15:43,621 - INFO - Returning successful result
2025-04-12 07:15:43,621 - INFO - Writing result to stdout
2025-04-12 07:15:43,622 - INFO - Result written successfully
2025-04-12 07:15:46,360 - INFO - Starting embedding helper script
2025-04-12 07:15:46,360 - INFO - Reading input from stdin
2025-04-12 07:15:46,361 - INFO - Input data length: 1949
2025-04-12 07:15:46,361 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1903
2025-04-12 07:15:46,362 - INFO - Processing text with length: 1903 chars
2025-04-12 07:15:46,362 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 07:15:46,362 - INFO - Text length: 1903
2025-04-12 07:15:51,968 - INFO - Torch threads set to 1
2025-04-12 07:16:06,784 - INFO - Loading model...
2025-04-12 07:16:06,792 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 07:16:11,368 - INFO - Model loaded successfully
2025-04-12 07:16:11,368 - INFO - Generating embedding...
2025-04-12 07:16:11,540 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 07:16:11,541 - INFO - Returning successful result
2025-04-12 07:16:11,542 - INFO - Writing result to stdout
2025-04-12 07:16:11,542 - INFO - Result written successfully
2025-04-12 07:16:13,776 - INFO - Starting embedding helper script
2025-04-12 07:16:13,776 - INFO - Reading input from stdin
2025-04-12 07:16:13,777 - INFO - Input data length: 1506
2025-04-12 07:16:13,777 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1460
2025-04-12 07:16:13,778 - INFO - Processing text with length: 1460 chars
2025-04-12 07:16:13,778 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 07:16:13,778 - INFO - Text length: 1460
2025-04-12 07:16:19,216 - INFO - Torch threads set to 1
2025-04-12 07:16:30,980 - INFO - Loading model...
2025-04-12 07:16:30,981 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 07:16:35,570 - INFO - Model loaded successfully
2025-04-12 07:16:35,571 - INFO - Generating embedding...
2025-04-12 07:16:35,998 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 07:16:36,000 - INFO - Returning successful result
2025-04-12 07:16:36,001 - INFO - Writing result to stdout
2025-04-12 07:16:36,002 - INFO - Result written successfully
2025-04-12 08:28:32,817 - INFO - Starting embedding helper script
2025-04-12 08:28:32,819 - INFO - Reading input from stdin
2025-04-12 08:28:32,820 - INFO - Input data length: 2013
2025-04-12 08:28:32,820 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1967
2025-04-12 08:28:32,821 - INFO - Processing text with length: 1967 chars
2025-04-12 08:28:32,821 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:28:32,822 - INFO - Text length: 1967
2025-04-12 08:28:46,510 - INFO - Torch threads set to 1
2025-04-12 08:29:06,112 - INFO - Loading model...
2025-04-12 08:29:06,113 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:29:12,003 - INFO - Model loaded successfully
2025-04-12 08:29:12,004 - INFO - Generating embedding...
2025-04-12 08:29:14,157 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:29:14,159 - INFO - Returning successful result
2025-04-12 08:29:14,159 - INFO - Writing result to stdout
2025-04-12 08:29:14,160 - INFO - Result written successfully
2025-04-12 08:29:18,870 - INFO - Starting embedding helper script
2025-04-12 08:29:18,870 - INFO - Reading input from stdin
2025-04-12 08:29:18,871 - INFO - Input data length: 599
2025-04-12 08:29:18,878 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=553
2025-04-12 08:29:18,884 - INFO - Processing text with length: 553 chars
2025-04-12 08:29:18,888 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:29:18,888 - INFO - Text length: 553
2025-04-12 08:29:27,289 - INFO - Torch threads set to 1
2025-04-12 08:29:43,898 - INFO - Loading model...
2025-04-12 08:29:43,903 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:29:49,114 - INFO - Model loaded successfully
2025-04-12 08:29:49,116 - INFO - Generating embedding...
2025-04-12 08:29:49,282 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:29:49,283 - INFO - Returning successful result
2025-04-12 08:29:49,285 - INFO - Writing result to stdout
2025-04-12 08:29:49,287 - INFO - Result written successfully
2025-04-12 08:29:52,354 - INFO - Starting embedding helper script
2025-04-12 08:29:52,354 - INFO - Reading input from stdin
2025-04-12 08:29:52,354 - INFO - Input data length: 2025
2025-04-12 08:29:52,354 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1979
2025-04-12 08:29:52,356 - INFO - Processing text with length: 1979 chars
2025-04-12 08:29:52,357 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:29:52,357 - INFO - Text length: 1979
2025-04-12 08:29:58,376 - INFO - Torch threads set to 1
2025-04-12 08:30:11,175 - INFO - Loading model...
2025-04-12 08:30:11,180 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:30:16,693 - INFO - Model loaded successfully
2025-04-12 08:30:16,694 - INFO - Generating embedding...
2025-04-12 08:30:17,098 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:30:17,120 - INFO - Returning successful result
2025-04-12 08:30:17,130 - INFO - Writing result to stdout
2025-04-12 08:30:17,144 - INFO - Result written successfully
2025-04-12 08:30:21,353 - INFO - Starting embedding helper script
2025-04-12 08:30:21,354 - INFO - Reading input from stdin
2025-04-12 08:30:21,354 - INFO - Input data length: 1009
2025-04-12 08:30:21,354 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=963
2025-04-12 08:30:21,354 - INFO - Processing text with length: 963 chars
2025-04-12 08:30:21,355 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:30:21,355 - INFO - Text length: 963
2025-04-12 08:30:28,190 - INFO - Torch threads set to 1
2025-04-12 08:30:44,160 - INFO - Loading model...
2025-04-12 08:30:44,161 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:30:49,873 - INFO - Model loaded successfully
2025-04-12 08:30:49,874 - INFO - Generating embedding...
2025-04-12 08:30:50,148 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:30:50,149 - INFO - Returning successful result
2025-04-12 08:30:50,150 - INFO - Writing result to stdout
2025-04-12 08:30:50,150 - INFO - Result written successfully
2025-04-12 08:34:48,310 - INFO - Starting embedding helper script
2025-04-12 08:34:48,310 - INFO - Reading input from stdin
2025-04-12 08:34:48,311 - INFO - Input data length: 2036
2025-04-12 08:34:48,311 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1990
2025-04-12 08:34:48,311 - INFO - Processing text with length: 1990 chars
2025-04-12 08:34:48,312 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:34:48,312 - INFO - Text length: 1990
2025-04-12 08:34:55,105 - INFO - Torch threads set to 1
2025-04-12 08:35:10,519 - INFO - Loading model...
2025-04-12 08:35:10,521 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:35:15,416 - INFO - Model loaded successfully
2025-04-12 08:35:15,416 - INFO - Generating embedding...
2025-04-12 08:35:15,811 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:35:15,814 - INFO - Returning successful result
2025-04-12 08:35:15,814 - INFO - Writing result to stdout
2025-04-12 08:35:15,816 - INFO - Result written successfully
2025-04-12 08:35:19,251 - INFO - Starting embedding helper script
2025-04-12 08:35:19,254 - INFO - Reading input from stdin
2025-04-12 08:35:19,256 - INFO - Input data length: 1732
2025-04-12 08:35:19,256 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1686
2025-04-12 08:35:19,257 - INFO - Processing text with length: 1686 chars
2025-04-12 08:35:19,257 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:35:19,257 - INFO - Text length: 1686
2025-04-12 08:35:28,113 - INFO - Torch threads set to 1
2025-04-12 08:35:46,726 - INFO - Loading model...
2025-04-12 08:35:46,739 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:35:51,616 - INFO - Model loaded successfully
2025-04-12 08:35:51,617 - INFO - Generating embedding...
2025-04-12 08:35:52,269 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:35:52,270 - INFO - Returning successful result
2025-04-12 08:35:52,271 - INFO - Writing result to stdout
2025-04-12 08:35:52,272 - INFO - Result written successfully
2025-04-12 08:35:56,659 - INFO - Starting embedding helper script
2025-04-12 08:35:56,663 - INFO - Reading input from stdin
2025-04-12 08:35:56,666 - INFO - Input data length: 2045
2025-04-12 08:35:56,667 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1999
2025-04-12 08:35:56,670 - INFO - Processing text with length: 1999 chars
2025-04-12 08:35:56,670 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:35:56,670 - INFO - Text length: 1999
2025-04-12 08:36:05,604 - INFO - Torch threads set to 1
2025-04-12 08:36:23,781 - INFO - Loading model...
2025-04-12 08:36:23,782 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:36:28,739 - INFO - Model loaded successfully
2025-04-12 08:36:28,739 - INFO - Generating embedding...
2025-04-12 08:36:29,136 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:36:29,139 - INFO - Returning successful result
2025-04-12 08:36:29,140 - INFO - Writing result to stdout
2025-04-12 08:36:29,142 - INFO - Result written successfully
2025-04-12 08:36:32,850 - INFO - Starting embedding helper script
2025-04-12 08:36:32,850 - INFO - Reading input from stdin
2025-04-12 08:36:32,850 - INFO - Input data length: 2044
2025-04-12 08:36:32,851 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1998
2025-04-12 08:36:32,851 - INFO - Processing text with length: 1998 chars
2025-04-12 08:36:32,851 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:36:32,852 - INFO - Text length: 1998
2025-04-12 08:36:40,732 - INFO - Torch threads set to 1
2025-04-12 08:36:57,126 - INFO - Loading model...
2025-04-12 08:36:57,131 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:37:02,006 - INFO - Model loaded successfully
2025-04-12 08:37:02,006 - INFO - Generating embedding...
2025-04-12 08:37:02,333 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:37:02,336 - INFO - Returning successful result
2025-04-12 08:37:02,343 - INFO - Writing result to stdout
2025-04-12 08:37:02,346 - INFO - Result written successfully
2025-04-12 08:37:05,916 - INFO - Starting embedding helper script
2025-04-12 08:37:05,917 - INFO - Reading input from stdin
2025-04-12 08:37:05,918 - INFO - Input data length: 867
2025-04-12 08:37:05,918 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=821
2025-04-12 08:37:05,918 - INFO - Processing text with length: 821 chars
2025-04-12 08:37:05,919 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:37:05,919 - INFO - Text length: 821
2025-04-12 08:37:13,595 - INFO - Torch threads set to 1
2025-04-12 08:37:28,085 - INFO - Loading model...
2025-04-12 08:37:28,091 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:37:33,259 - INFO - Model loaded successfully
2025-04-12 08:37:33,259 - INFO - Generating embedding...
2025-04-12 08:37:33,550 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:37:33,560 - INFO - Returning successful result
2025-04-12 08:37:33,565 - INFO - Writing result to stdout
2025-04-12 08:37:33,566 - INFO - Result written successfully
2025-04-12 08:37:37,286 - INFO - Starting embedding helper script
2025-04-12 08:37:37,286 - INFO - Reading input from stdin
2025-04-12 08:37:37,287 - INFO - Input data length: 1949
2025-04-12 08:37:37,287 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1903
2025-04-12 08:37:37,287 - INFO - Processing text with length: 1903 chars
2025-04-12 08:37:37,288 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:37:37,288 - INFO - Text length: 1903
2025-04-12 08:37:44,663 - INFO - Torch threads set to 1
2025-04-12 08:37:59,916 - INFO - Loading model...
2025-04-12 08:37:59,933 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:38:05,122 - INFO - Model loaded successfully
2025-04-12 08:38:05,122 - INFO - Generating embedding...
2025-04-12 08:38:05,446 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:38:05,447 - INFO - Returning successful result
2025-04-12 08:38:05,448 - INFO - Writing result to stdout
2025-04-12 08:38:05,448 - INFO - Result written successfully
2025-04-12 08:38:08,890 - INFO - Starting embedding helper script
2025-04-12 08:38:08,890 - INFO - Reading input from stdin
2025-04-12 08:38:08,890 - INFO - Input data length: 1506
2025-04-12 08:38:08,891 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1460
2025-04-12 08:38:08,891 - INFO - Processing text with length: 1460 chars
2025-04-12 08:38:08,892 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:38:08,892 - INFO - Text length: 1460
2025-04-12 08:38:16,166 - INFO - Torch threads set to 1
2025-04-12 08:38:32,127 - INFO - Loading model...
2025-04-12 08:38:32,128 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:38:37,444 - INFO - Model loaded successfully
2025-04-12 08:38:37,444 - INFO - Generating embedding...
2025-04-12 08:38:37,745 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:38:37,798 - INFO - Returning successful result
2025-04-12 08:38:37,800 - INFO - Writing result to stdout
2025-04-12 08:38:37,802 - INFO - Result written successfully
2025-04-12 08:41:14,040 - INFO - Starting embedding helper script
2025-04-12 08:41:14,040 - INFO - Reading input from stdin
2025-04-12 08:41:14,041 - INFO - Input data length: 9473
2025-04-12 08:41:14,041 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1922
2025-04-12 08:41:14,041 - INFO - Processing text with length: 1922 chars
2025-04-12 08:41:14,042 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:41:14,042 - INFO - Text length: 1922
2025-04-12 08:41:22,126 - INFO - Torch threads set to 1
2025-04-12 08:41:37,590 - INFO - Loading model...
2025-04-12 08:41:37,597 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:41:42,526 - INFO - Model loaded successfully
2025-04-12 08:41:42,526 - INFO - Generating embedding...
2025-04-12 08:41:42,856 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:41:42,867 - INFO - Returning successful result
2025-04-12 08:41:42,867 - INFO - Writing result to stdout
2025-04-12 08:41:42,868 - INFO - Result written successfully
2025-04-12 08:41:46,963 - INFO - Starting embedding helper script
2025-04-12 08:41:46,963 - INFO - Reading input from stdin
2025-04-12 08:41:46,964 - INFO - Input data length: 6088
2025-04-12 08:41:46,964 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1222
2025-04-12 08:41:46,972 - INFO - Processing text with length: 1222 chars
2025-04-12 08:41:46,973 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:41:46,973 - INFO - Text length: 1222
2025-04-12 08:41:54,931 - INFO - Torch threads set to 1
2025-04-12 08:42:12,065 - INFO - Loading model...
2025-04-12 08:42:12,067 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:42:17,579 - INFO - Model loaded successfully
2025-04-12 08:42:17,579 - INFO - Generating embedding...
2025-04-12 08:42:17,854 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:42:17,855 - INFO - Returning successful result
2025-04-12 08:42:17,856 - INFO - Writing result to stdout
2025-04-12 08:42:17,856 - INFO - Result written successfully
2025-04-12 08:42:21,818 - INFO - Starting embedding helper script
2025-04-12 08:42:21,818 - INFO - Reading input from stdin
2025-04-12 08:42:21,819 - INFO - Input data length: 9633
2025-04-12 08:42:21,819 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1967
2025-04-12 08:42:21,819 - INFO - Processing text with length: 1967 chars
2025-04-12 08:42:21,820 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:42:21,820 - INFO - Text length: 1967
2025-04-12 08:42:29,899 - INFO - Torch threads set to 1
2025-04-12 08:42:44,934 - INFO - Loading model...
2025-04-12 08:42:44,935 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:42:49,835 - INFO - Model loaded successfully
2025-04-12 08:42:49,835 - INFO - Generating embedding...
2025-04-12 08:42:50,189 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:42:50,194 - INFO - Returning successful result
2025-04-12 08:42:50,194 - INFO - Writing result to stdout
2025-04-12 08:42:50,195 - INFO - Result written successfully
2025-04-12 08:42:53,599 - INFO - Starting embedding helper script
2025-04-12 08:42:53,600 - INFO - Reading input from stdin
2025-04-12 08:42:53,600 - INFO - Input data length: 9726
2025-04-12 08:42:53,600 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1985
2025-04-12 08:42:53,601 - INFO - Processing text with length: 1985 chars
2025-04-12 08:42:53,601 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:42:53,601 - INFO - Text length: 1985
2025-04-12 08:43:01,274 - INFO - Torch threads set to 1
2025-04-12 08:43:16,843 - INFO - Loading model...
2025-04-12 08:43:16,847 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:43:22,054 - INFO - Model loaded successfully
2025-04-12 08:43:22,054 - INFO - Generating embedding...
2025-04-12 08:43:22,319 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:43:22,334 - INFO - Returning successful result
2025-04-12 08:43:22,336 - INFO - Writing result to stdout
2025-04-12 08:43:22,344 - INFO - Result written successfully
2025-04-12 08:43:25,848 - INFO - Starting embedding helper script
2025-04-12 08:43:25,848 - INFO - Reading input from stdin
2025-04-12 08:43:25,849 - INFO - Input data length: 9826
2025-04-12 08:43:25,849 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1980
2025-04-12 08:43:25,849 - INFO - Processing text with length: 1980 chars
2025-04-12 08:43:25,850 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:43:25,850 - INFO - Text length: 1980
2025-04-12 08:43:33,191 - INFO - Torch threads set to 1
2025-04-12 08:43:47,897 - INFO - Loading model...
2025-04-12 08:43:47,902 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:43:53,133 - INFO - Model loaded successfully
2025-04-12 08:43:53,133 - INFO - Generating embedding...
2025-04-12 08:43:53,615 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:43:53,618 - INFO - Returning successful result
2025-04-12 08:43:53,618 - INFO - Writing result to stdout
2025-04-12 08:43:53,618 - INFO - Result written successfully
2025-04-12 08:43:56,998 - INFO - Starting embedding helper script
2025-04-12 08:43:56,999 - INFO - Reading input from stdin
2025-04-12 08:43:56,999 - INFO - Input data length: 1947
2025-04-12 08:43:56,999 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=371
2025-04-12 08:43:57,000 - INFO - Processing text with length: 371 chars
2025-04-12 08:43:57,000 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:43:57,000 - INFO - Text length: 371
2025-04-12 08:44:04,553 - INFO - Torch threads set to 1
2025-04-12 08:44:19,996 - INFO - Loading model...
2025-04-12 08:44:19,997 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:44:25,206 - INFO - Model loaded successfully
2025-04-12 08:44:25,207 - INFO - Generating embedding...
2025-04-12 08:44:25,263 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:44:25,264 - INFO - Returning successful result
2025-04-12 08:44:25,265 - INFO - Writing result to stdout
2025-04-12 08:44:25,265 - INFO - Result written successfully
2025-04-12 08:44:30,559 - INFO - Starting embedding helper script
2025-04-12 08:44:30,562 - INFO - Reading input from stdin
2025-04-12 08:44:30,563 - INFO - Input data length: 9090
2025-04-12 08:44:30,564 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1884
2025-04-12 08:44:30,564 - INFO - Processing text with length: 1884 chars
2025-04-12 08:44:30,564 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:44:30,565 - INFO - Text length: 1884
2025-04-12 08:44:40,260 - INFO - Torch threads set to 1
2025-04-12 08:44:58,347 - INFO - Loading model...
2025-04-12 08:44:58,349 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:45:03,898 - INFO - Model loaded successfully
2025-04-12 08:45:03,899 - INFO - Generating embedding...
2025-04-12 08:45:04,752 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:45:04,758 - INFO - Returning successful result
2025-04-12 08:45:04,759 - INFO - Writing result to stdout
2025-04-12 08:45:04,760 - INFO - Result written successfully
2025-04-12 08:45:10,921 - INFO - Starting embedding helper script
2025-04-12 08:45:10,921 - INFO - Reading input from stdin
2025-04-12 08:45:10,922 - INFO - Input data length: 9477
2025-04-12 08:45:10,923 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1966
2025-04-12 08:45:10,923 - INFO - Processing text with length: 1966 chars
2025-04-12 08:45:10,923 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:45:10,924 - INFO - Text length: 1966
2025-04-12 08:45:22,372 - INFO - Torch threads set to 1
2025-04-12 08:45:43,811 - INFO - Loading model...
2025-04-12 08:45:43,812 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:45:49,915 - INFO - Model loaded successfully
2025-04-12 08:45:49,915 - INFO - Generating embedding...
2025-04-12 08:45:50,268 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:45:50,269 - INFO - Returning successful result
2025-04-12 08:45:50,269 - INFO - Writing result to stdout
2025-04-12 08:45:50,270 - INFO - Result written successfully
2025-04-12 08:45:55,839 - INFO - Starting embedding helper script
2025-04-12 08:45:55,839 - INFO - Reading input from stdin
2025-04-12 08:45:55,839 - INFO - Input data length: 8516
2025-04-12 08:45:55,841 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1695
2025-04-12 08:45:55,842 - INFO - Processing text with length: 1695 chars
2025-04-12 08:45:55,843 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:45:55,843 - INFO - Text length: 1695
2025-04-12 08:46:08,232 - INFO - Torch threads set to 1
2025-04-12 08:46:36,882 - INFO - Loading model...
2025-04-12 08:46:36,884 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:46:42,787 - INFO - Model loaded successfully
2025-04-12 08:46:42,787 - INFO - Generating embedding...
2025-04-12 08:46:43,342 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:46:43,343 - INFO - Returning successful result
2025-04-12 08:46:43,344 - INFO - Writing result to stdout
2025-04-12 08:46:43,344 - INFO - Result written successfully
2025-04-12 08:46:48,460 - INFO - Starting embedding helper script
2025-04-12 08:46:48,461 - INFO - Reading input from stdin
2025-04-12 08:46:48,461 - INFO - Input data length: 9589
2025-04-12 08:46:48,462 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1963
2025-04-12 08:46:48,462 - INFO - Processing text with length: 1963 chars
2025-04-12 08:46:48,462 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:46:48,463 - INFO - Text length: 1963
2025-04-12 08:46:59,582 - INFO - Torch threads set to 1
2025-04-12 08:47:17,704 - INFO - Loading model...
2025-04-12 08:47:17,705 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:47:22,983 - INFO - Model loaded successfully
2025-04-12 08:47:22,984 - INFO - Generating embedding...
2025-04-12 08:47:24,013 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:47:24,014 - INFO - Returning successful result
2025-04-12 08:47:24,014 - INFO - Writing result to stdout
2025-04-12 08:47:24,017 - INFO - Result written successfully
2025-04-12 08:47:28,343 - INFO - Starting embedding helper script
2025-04-12 08:47:28,343 - INFO - Reading input from stdin
2025-04-12 08:47:28,343 - INFO - Input data length: 9573
2025-04-12 08:47:28,344 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1967
2025-04-12 08:47:28,344 - INFO - Processing text with length: 1967 chars
2025-04-12 08:47:28,344 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:47:28,345 - INFO - Text length: 1967
2025-04-12 08:47:37,322 - INFO - Torch threads set to 1
2025-04-12 08:47:54,318 - INFO - Loading model...
2025-04-12 08:47:54,323 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:47:59,173 - INFO - Model loaded successfully
2025-04-12 08:47:59,173 - INFO - Generating embedding...
2025-04-12 08:47:59,643 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:47:59,649 - INFO - Returning successful result
2025-04-12 08:47:59,651 - INFO - Writing result to stdout
2025-04-12 08:47:59,652 - INFO - Result written successfully
2025-04-12 08:48:05,151 - INFO - Starting embedding helper script
2025-04-12 08:48:05,152 - INFO - Reading input from stdin
2025-04-12 08:48:05,152 - INFO - Input data length: 9313
2025-04-12 08:48:05,153 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1902
2025-04-12 08:48:05,153 - INFO - Processing text with length: 1902 chars
2025-04-12 08:48:05,154 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:48:05,155 - INFO - Text length: 1902
2025-04-12 08:48:14,768 - INFO - Torch threads set to 1
2025-04-12 08:48:33,783 - INFO - Loading model...
2025-04-12 08:48:33,784 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:48:38,766 - INFO - Model loaded successfully
2025-04-12 08:48:38,766 - INFO - Generating embedding...
2025-04-12 08:48:39,115 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:48:39,119 - INFO - Returning successful result
2025-04-12 08:48:39,120 - INFO - Writing result to stdout
2025-04-12 08:48:39,120 - INFO - Result written successfully
2025-04-12 08:48:43,273 - INFO - Starting embedding helper script
2025-04-12 08:48:43,274 - INFO - Reading input from stdin
2025-04-12 08:48:43,274 - INFO - Input data length: 9129
2025-04-12 08:48:43,274 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1853
2025-04-12 08:48:43,275 - INFO - Processing text with length: 1853 chars
2025-04-12 08:48:43,275 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:48:43,275 - INFO - Text length: 1853
2025-04-12 08:48:51,841 - INFO - Torch threads set to 1
2025-04-12 08:49:09,888 - INFO - Loading model...
2025-04-12 08:49:09,890 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:49:15,006 - INFO - Model loaded successfully
2025-04-12 08:49:15,006 - INFO - Generating embedding...
2025-04-12 08:49:15,553 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:49:15,555 - INFO - Returning successful result
2025-04-12 08:49:15,555 - INFO - Writing result to stdout
2025-04-12 08:49:15,555 - INFO - Result written successfully
2025-04-12 08:49:20,186 - INFO - Starting embedding helper script
2025-04-12 08:49:20,187 - INFO - Reading input from stdin
2025-04-12 08:49:20,187 - INFO - Input data length: 4649
2025-04-12 08:49:20,188 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=918
2025-04-12 08:49:20,188 - INFO - Processing text with length: 918 chars
2025-04-12 08:49:20,189 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:49:20,189 - INFO - Text length: 918
2025-04-12 08:49:29,588 - INFO - Torch threads set to 1
2025-04-12 08:49:53,398 - INFO - Loading model...
2025-04-12 08:49:53,400 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:49:58,839 - INFO - Model loaded successfully
2025-04-12 08:49:58,840 - INFO - Generating embedding...
2025-04-12 08:49:59,156 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:49:59,161 - INFO - Returning successful result
2025-04-12 08:49:59,161 - INFO - Writing result to stdout
2025-04-12 08:49:59,161 - INFO - Result written successfully
2025-04-12 08:50:04,287 - INFO - Starting embedding helper script
2025-04-12 08:50:04,288 - INFO - Reading input from stdin
2025-04-12 08:50:04,288 - INFO - Input data length: 9487
2025-04-12 08:50:04,289 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1991
2025-04-12 08:50:04,290 - INFO - Processing text with length: 1991 chars
2025-04-12 08:50:04,290 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:50:04,291 - INFO - Text length: 1991
2025-04-12 08:50:13,837 - INFO - Torch threads set to 1
2025-04-12 08:50:36,241 - INFO - Loading model...
2025-04-12 08:50:36,255 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:50:41,685 - INFO - Model loaded successfully
2025-04-12 08:50:41,686 - INFO - Generating embedding...
2025-04-12 08:50:42,215 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:50:42,217 - INFO - Returning successful result
2025-04-12 08:50:42,218 - INFO - Writing result to stdout
2025-04-12 08:50:42,219 - INFO - Result written successfully
2025-04-12 08:50:48,555 - INFO - Starting embedding helper script
2025-04-12 08:50:48,556 - INFO - Reading input from stdin
2025-04-12 08:50:48,556 - INFO - Input data length: 9582
2025-04-12 08:50:48,557 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1976
2025-04-12 08:50:48,557 - INFO - Processing text with length: 1976 chars
2025-04-12 08:50:48,557 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:50:48,558 - INFO - Text length: 1976
2025-04-12 08:51:05,164 - INFO - Torch threads set to 1
2025-04-12 08:51:28,243 - INFO - Loading model...
2025-04-12 08:51:28,247 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:51:34,290 - INFO - Model loaded successfully
2025-04-12 08:51:34,290 - INFO - Generating embedding...
2025-04-12 08:51:34,972 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:51:34,973 - INFO - Returning successful result
2025-04-12 08:51:34,973 - INFO - Writing result to stdout
2025-04-12 08:51:34,975 - INFO - Result written successfully
2025-04-12 08:51:42,092 - INFO - Starting embedding helper script
2025-04-12 08:51:42,092 - INFO - Reading input from stdin
2025-04-12 08:51:42,093 - INFO - Input data length: 8502
2025-04-12 08:51:42,094 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1731
2025-04-12 08:51:42,094 - INFO - Processing text with length: 1731 chars
2025-04-12 08:51:42,095 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:51:42,096 - INFO - Text length: 1731
2025-04-12 08:51:54,985 - INFO - Torch threads set to 1
2025-04-12 08:52:17,037 - INFO - Loading model...
2025-04-12 08:52:17,042 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:52:22,702 - INFO - Model loaded successfully
2025-04-12 08:52:22,702 - INFO - Generating embedding...
2025-04-12 08:52:23,193 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:52:23,194 - INFO - Returning successful result
2025-04-12 08:52:23,194 - INFO - Writing result to stdout
2025-04-12 08:52:23,194 - INFO - Result written successfully
2025-04-12 08:52:28,001 - INFO - Starting embedding helper script
2025-04-12 08:52:28,006 - INFO - Reading input from stdin
2025-04-12 08:52:28,010 - INFO - Input data length: 9012
2025-04-12 08:52:28,011 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1886
2025-04-12 08:52:28,012 - INFO - Processing text with length: 1886 chars
2025-04-12 08:52:28,012 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:52:28,012 - INFO - Text length: 1886
2025-04-12 08:52:37,437 - INFO - Torch threads set to 1
2025-04-12 08:52:56,603 - INFO - Loading model...
2025-04-12 08:52:56,607 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:53:02,030 - INFO - Model loaded successfully
2025-04-12 08:53:02,030 - INFO - Generating embedding...
2025-04-12 08:53:02,490 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:53:02,497 - INFO - Returning successful result
2025-04-12 08:53:02,500 - INFO - Writing result to stdout
2025-04-12 08:53:02,501 - INFO - Result written successfully
2025-04-12 08:53:07,225 - INFO - Starting embedding helper script
2025-04-12 08:53:07,225 - INFO - Reading input from stdin
2025-04-12 08:53:07,226 - INFO - Input data length: 7035
2025-04-12 08:53:07,226 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1434
2025-04-12 08:53:07,226 - INFO - Processing text with length: 1434 chars
2025-04-12 08:53:07,227 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:53:07,227 - INFO - Text length: 1434
2025-04-12 08:53:18,018 - INFO - Torch threads set to 1
2025-04-12 08:53:40,110 - INFO - Loading model...
2025-04-12 08:53:40,116 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:53:47,362 - INFO - Model loaded successfully
2025-04-12 08:53:47,363 - INFO - Generating embedding...
2025-04-12 08:53:49,059 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:53:49,060 - INFO - Returning successful result
2025-04-12 08:53:49,061 - INFO - Writing result to stdout
2025-04-12 08:53:49,061 - INFO - Result written successfully
2025-04-12 08:53:56,397 - INFO - Starting embedding helper script
2025-04-12 08:53:56,405 - INFO - Reading input from stdin
2025-04-12 08:53:56,417 - INFO - Input data length: 9090
2025-04-12 08:53:56,422 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1884
2025-04-12 08:53:56,423 - INFO - Processing text with length: 1884 chars
2025-04-12 08:53:56,427 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:53:56,436 - INFO - Text length: 1884
2025-04-12 08:54:18,763 - INFO - Torch threads set to 1
2025-04-12 08:54:38,564 - INFO - Loading model...
2025-04-12 08:54:38,571 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:54:43,892 - INFO - Model loaded successfully
2025-04-12 08:54:43,893 - INFO - Generating embedding...
2025-04-12 08:54:44,286 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:54:44,287 - INFO - Returning successful result
2025-04-12 08:54:44,287 - INFO - Writing result to stdout
2025-04-12 08:54:44,288 - INFO - Result written successfully
2025-04-12 08:54:48,629 - INFO - Starting embedding helper script
2025-04-12 08:54:48,630 - INFO - Reading input from stdin
2025-04-12 08:54:48,632 - INFO - Input data length: 9477
2025-04-12 08:54:48,638 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1966
2025-04-12 08:54:48,638 - INFO - Processing text with length: 1966 chars
2025-04-12 08:54:48,639 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:54:48,639 - INFO - Text length: 1966
2025-04-12 08:55:00,947 - INFO - Torch threads set to 1
2025-04-12 08:55:20,720 - INFO - Loading model...
2025-04-12 08:55:20,721 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:55:25,998 - INFO - Model loaded successfully
2025-04-12 08:55:25,999 - INFO - Generating embedding...
2025-04-12 08:55:26,386 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:55:26,391 - INFO - Returning successful result
2025-04-12 08:55:26,421 - INFO - Writing result to stdout
2025-04-12 08:55:26,421 - INFO - Result written successfully
2025-04-12 08:55:31,672 - INFO - Starting embedding helper script
2025-04-12 08:55:31,673 - INFO - Reading input from stdin
2025-04-12 08:55:31,673 - INFO - Input data length: 8516
2025-04-12 08:55:31,674 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1695
2025-04-12 08:55:31,674 - INFO - Processing text with length: 1695 chars
2025-04-12 08:55:31,674 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:55:31,675 - INFO - Text length: 1695
2025-04-12 08:55:40,286 - INFO - Torch threads set to 1
2025-04-12 08:56:02,526 - INFO - Loading model...
2025-04-12 08:56:02,528 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:56:08,540 - INFO - Model loaded successfully
2025-04-12 08:56:08,540 - INFO - Generating embedding...
2025-04-12 08:56:09,087 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:56:09,101 - INFO - Returning successful result
2025-04-12 08:56:09,102 - INFO - Writing result to stdout
2025-04-12 08:56:09,107 - INFO - Result written successfully
2025-04-12 08:56:15,765 - INFO - Starting embedding helper script
2025-04-12 08:56:15,766 - INFO - Reading input from stdin
2025-04-12 08:56:15,766 - INFO - Input data length: 9773
2025-04-12 08:56:15,767 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1992
2025-04-12 08:56:15,767 - INFO - Processing text with length: 1992 chars
2025-04-12 08:56:15,767 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:56:15,768 - INFO - Text length: 1992
2025-04-12 08:56:25,441 - INFO - Torch threads set to 1
2025-04-12 08:56:43,517 - INFO - Loading model...
2025-04-12 08:56:43,521 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:56:48,267 - INFO - Model loaded successfully
2025-04-12 08:56:48,267 - INFO - Generating embedding...
2025-04-12 08:56:48,716 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:56:48,717 - INFO - Returning successful result
2025-04-12 08:56:48,717 - INFO - Writing result to stdout
2025-04-12 08:56:48,718 - INFO - Result written successfully
2025-04-12 08:56:53,310 - INFO - Starting embedding helper script
2025-04-12 08:56:53,313 - INFO - Reading input from stdin
2025-04-12 08:56:53,314 - INFO - Input data length: 9512
2025-04-12 08:56:53,319 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1931
2025-04-12 08:56:53,320 - INFO - Processing text with length: 1931 chars
2025-04-12 08:56:53,323 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:56:53,323 - INFO - Text length: 1931
2025-04-12 08:57:03,780 - INFO - Torch threads set to 1
2025-04-12 08:57:23,376 - INFO - Loading model...
2025-04-12 08:57:23,381 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:57:29,538 - INFO - Model loaded successfully
2025-04-12 08:57:29,538 - INFO - Generating embedding...
2025-04-12 08:57:29,952 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:57:30,025 - INFO - Returning successful result
2025-04-12 08:57:30,025 - INFO - Writing result to stdout
2025-04-12 08:57:30,026 - INFO - Result written successfully
2025-04-12 08:57:34,228 - INFO - Starting embedding helper script
2025-04-12 08:57:34,228 - INFO - Reading input from stdin
2025-04-12 08:57:34,229 - INFO - Input data length: 9900
2025-04-12 08:57:34,229 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1994
2025-04-12 08:57:34,229 - INFO - Processing text with length: 1994 chars
2025-04-12 08:57:34,230 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:57:34,230 - INFO - Text length: 1994
2025-04-12 08:57:43,608 - INFO - Torch threads set to 1
2025-04-12 08:58:01,428 - INFO - Loading model...
2025-04-12 08:58:01,430 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:58:06,178 - INFO - Model loaded successfully
2025-04-12 08:58:06,179 - INFO - Generating embedding...
2025-04-12 08:58:06,556 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:58:06,570 - INFO - Returning successful result
2025-04-12 08:58:06,570 - INFO - Writing result to stdout
2025-04-12 08:58:06,571 - INFO - Result written successfully
2025-04-12 08:58:10,497 - INFO - Starting embedding helper script
2025-04-12 08:58:10,497 - INFO - Reading input from stdin
2025-04-12 08:58:10,498 - INFO - Input data length: 9044
2025-04-12 08:58:10,498 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1943
2025-04-12 08:58:10,498 - INFO - Processing text with length: 1943 chars
2025-04-12 08:58:10,499 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:58:10,499 - INFO - Text length: 1943
2025-04-12 08:58:19,637 - INFO - Torch threads set to 1
2025-04-12 08:58:36,486 - INFO - Loading model...
2025-04-12 08:58:36,487 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:58:41,676 - INFO - Model loaded successfully
2025-04-12 08:58:41,676 - INFO - Generating embedding...
2025-04-12 08:58:42,120 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:58:42,121 - INFO - Returning successful result
2025-04-12 08:58:42,121 - INFO - Writing result to stdout
2025-04-12 08:58:42,123 - INFO - Result written successfully
2025-04-12 08:58:46,660 - INFO - Starting embedding helper script
2025-04-12 08:58:46,660 - INFO - Reading input from stdin
2025-04-12 08:58:46,661 - INFO - Input data length: 6230
2025-04-12 08:58:46,661 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1244
2025-04-12 08:58:46,661 - INFO - Processing text with length: 1244 chars
2025-04-12 08:58:46,662 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:58:46,662 - INFO - Text length: 1244
2025-04-12 08:58:55,125 - INFO - Torch threads set to 1
2025-04-12 08:59:12,065 - INFO - Loading model...
2025-04-12 08:59:12,068 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:59:17,342 - INFO - Model loaded successfully
2025-04-12 08:59:17,342 - INFO - Generating embedding...
2025-04-12 08:59:17,748 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:59:17,749 - INFO - Returning successful result
2025-04-12 08:59:17,749 - INFO - Writing result to stdout
2025-04-12 08:59:17,750 - INFO - Result written successfully
2025-04-12 08:59:23,218 - INFO - Starting embedding helper script
2025-04-12 08:59:23,218 - INFO - Reading input from stdin
2025-04-12 08:59:23,219 - INFO - Input data length: 9413
2025-04-12 08:59:23,219 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1947
2025-04-12 08:59:23,219 - INFO - Processing text with length: 1947 chars
2025-04-12 08:59:23,220 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:59:23,220 - INFO - Text length: 1947
2025-04-12 08:59:32,137 - INFO - Torch threads set to 1
2025-04-12 08:59:49,453 - INFO - Loading model...
2025-04-12 08:59:49,455 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 08:59:54,483 - INFO - Model loaded successfully
2025-04-12 08:59:54,483 - INFO - Generating embedding...
2025-04-12 08:59:54,837 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 08:59:54,840 - INFO - Returning successful result
2025-04-12 08:59:54,840 - INFO - Writing result to stdout
2025-04-12 08:59:54,841 - INFO - Result written successfully
2025-04-12 08:59:58,955 - INFO - Starting embedding helper script
2025-04-12 08:59:58,955 - INFO - Reading input from stdin
2025-04-12 08:59:58,955 - INFO - Input data length: 9031
2025-04-12 08:59:58,956 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=1840
2025-04-12 08:59:58,956 - INFO - Processing text with length: 1840 chars
2025-04-12 08:59:58,957 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 08:59:58,957 - INFO - Text length: 1840
2025-04-12 09:00:07,263 - INFO - Torch threads set to 1
2025-04-12 09:00:24,320 - INFO - Loading model...
2025-04-12 09:00:24,327 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 09:00:30,070 - INFO - Model loaded successfully
2025-04-12 09:00:30,071 - INFO - Generating embedding...
2025-04-12 09:00:30,504 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 09:00:30,508 - INFO - Returning successful result
2025-04-12 09:00:30,508 - INFO - Writing result to stdout
2025-04-12 09:00:30,511 - INFO - Result written successfully
2025-04-12 09:48:41,702 - INFO - Starting embedding helper script
2025-04-12 09:48:41,703 - INFO - Reading input from stdin
2025-04-12 09:48:41,703 - INFO - Input data length: 106
2025-04-12 09:48:41,704 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=60
2025-04-12 09:48:41,704 - INFO - Processing text with length: 60 chars
2025-04-12 09:48:41,704 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 09:48:41,705 - INFO - Text length: 60
2025-04-12 09:48:53,722 - INFO - Torch threads set to 1
2025-04-12 09:49:14,019 - INFO - Loading model...
2025-04-12 09:49:14,021 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 09:49:19,505 - INFO - Model loaded successfully
2025-04-12 09:49:19,506 - INFO - Generating embedding...
2025-04-12 09:49:19,773 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 09:49:19,774 - INFO - Returning successful result
2025-04-12 09:49:19,774 - INFO - Writing result to stdout
2025-04-12 09:49:19,775 - INFO - Result written successfully
2025-04-12 09:52:52,468 - INFO - Starting embedding helper script
2025-04-12 09:52:52,469 - INFO - Reading input from stdin
2025-04-12 09:52:52,469 - INFO - Input data length: 99
2025-04-12 09:52:52,469 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=53
2025-04-12 09:52:52,470 - INFO - Processing text with length: 53 chars
2025-04-12 09:52:52,470 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 09:52:52,470 - INFO - Text length: 53
2025-04-12 09:53:02,337 - INFO - Torch threads set to 1
2025-04-12 09:53:21,091 - INFO - Loading model...
2025-04-12 09:53:21,093 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 09:53:26,551 - INFO - Model loaded successfully
2025-04-12 09:53:26,551 - INFO - Generating embedding...
2025-04-12 09:53:26,590 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 09:53:26,591 - INFO - Returning successful result
2025-04-12 09:53:26,591 - INFO - Writing result to stdout
2025-04-12 09:53:26,592 - INFO - Result written successfully
2025-04-12 09:55:26,066 - INFO - Starting embedding helper script
2025-04-12 09:55:26,067 - INFO - Reading input from stdin
2025-04-12 09:55:26,067 - INFO - Input data length: 87
2025-04-12 09:55:26,067 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=39
2025-04-12 09:55:26,068 - INFO - Processing text with length: 39 chars
2025-04-12 09:55:26,068 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 09:55:26,069 - INFO - Text length: 39
2025-04-12 09:55:36,484 - INFO - Torch threads set to 1
2025-04-12 09:55:56,226 - INFO - Loading model...
2025-04-12 09:55:56,228 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 09:56:01,453 - INFO - Model loaded successfully
2025-04-12 09:56:01,454 - INFO - Generating embedding...
2025-04-12 09:56:01,510 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 09:56:01,511 - INFO - Returning successful result
2025-04-12 09:56:01,511 - INFO - Writing result to stdout
2025-04-12 09:56:01,511 - INFO - Result written successfully
2025-04-12 09:57:54,469 - INFO - Starting embedding helper script
2025-04-12 09:57:54,469 - INFO - Reading input from stdin
2025-04-12 09:57:54,469 - INFO - Input data length: 113
2025-04-12 09:57:54,470 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=65
2025-04-12 09:57:54,470 - INFO - Processing text with length: 65 chars
2025-04-12 09:57:54,470 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 09:57:54,471 - INFO - Text length: 65
2025-04-12 09:58:05,325 - INFO - Torch threads set to 1
2025-04-12 09:58:26,341 - INFO - Loading model...
2025-04-12 09:58:26,343 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 09:58:31,906 - INFO - Model loaded successfully
2025-04-12 09:58:31,907 - INFO - Generating embedding...
2025-04-12 09:58:31,949 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 09:58:31,951 - INFO - Returning successful result
2025-04-12 09:58:31,951 - INFO - Writing result to stdout
2025-04-12 09:58:31,951 - INFO - Result written successfully
2025-04-12 10:37:55,351 - INFO - Starting embedding helper script
2025-04-12 10:37:55,351 - INFO - Reading input from stdin
2025-04-12 10:37:55,367 - INFO - Input data length: 293
2025-04-12 10:37:55,374 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=47
2025-04-12 10:37:55,378 - INFO - Processing text with length: 47 chars
2025-04-12 10:37:55,381 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 10:37:55,381 - INFO - Text length: 47
2025-04-12 10:38:10,671 - INFO - Torch threads set to 1
2025-04-12 10:38:38,331 - INFO - Loading model...
2025-04-12 10:38:38,332 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 10:38:44,719 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-04-12 10:39:01,691 - INFO - Model loaded successfully
2025-04-12 10:39:01,691 - INFO - Generating embedding...
2025-04-12 10:39:01,761 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 10:39:01,762 - INFO - Returning successful result
2025-04-12 10:39:01,763 - INFO - Writing result to stdout
2025-04-12 10:39:01,763 - INFO - Result written successfully
2025-04-12 10:41:33,326 - INFO - Starting embedding helper script
2025-04-12 10:41:33,327 - INFO - Reading input from stdin
2025-04-12 10:41:33,328 - INFO - Input data length: 334
2025-04-12 10:41:33,331 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=58
2025-04-12 10:41:33,332 - INFO - Processing text with length: 58 chars
2025-04-12 10:41:33,336 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 10:41:33,338 - INFO - Text length: 58
2025-04-12 10:41:44,771 - INFO - Torch threads set to 1
2025-04-12 10:42:13,933 - INFO - Loading model...
2025-04-12 10:42:13,934 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 10:42:21,167 - INFO - Model loaded successfully
2025-04-12 10:42:21,167 - INFO - Generating embedding...
2025-04-12 10:42:21,205 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 10:42:21,206 - INFO - Returning successful result
2025-04-12 10:42:21,207 - INFO - Writing result to stdout
2025-04-12 10:42:21,207 - INFO - Result written successfully
2025-04-12 10:44:52,662 - INFO - Starting embedding helper script
2025-04-12 10:44:52,662 - INFO - Reading input from stdin
2025-04-12 10:44:52,662 - INFO - Input data length: 129
2025-04-12 10:44:52,663 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=81
2025-04-12 10:44:52,663 - INFO - Processing text with length: 81 chars
2025-04-12 10:44:52,668 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 10:44:52,669 - INFO - Text length: 81
2025-04-12 10:45:05,324 - INFO - Torch threads set to 1
2025-04-12 10:45:29,043 - INFO - Loading model...
2025-04-12 10:45:29,044 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 10:45:35,308 - INFO - Model loaded successfully
2025-04-12 10:45:35,308 - INFO - Generating embedding...
2025-04-12 10:45:35,343 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 10:45:35,345 - INFO - Returning successful result
2025-04-12 10:45:35,345 - INFO - Writing result to stdout
2025-04-12 10:45:35,345 - INFO - Result written successfully
2025-04-12 10:47:42,498 - INFO - Starting embedding helper script
2025-04-12 10:47:42,498 - INFO - Reading input from stdin
2025-04-12 10:47:42,499 - INFO - Input data length: 93
2025-04-12 10:47:42,499 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=47
2025-04-12 10:47:42,499 - INFO - Processing text with length: 47 chars
2025-04-12 10:47:42,500 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 10:47:42,500 - INFO - Text length: 47
2025-04-12 10:47:54,306 - INFO - Torch threads set to 1
2025-04-12 10:48:25,194 - INFO - Loading model...
2025-04-12 10:48:25,196 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 10:48:32,981 - INFO - Model loaded successfully
2025-04-12 10:48:32,982 - INFO - Generating embedding...
2025-04-12 10:48:33,052 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 10:48:33,053 - INFO - Returning successful result
2025-04-12 10:48:33,054 - INFO - Writing result to stdout
2025-04-12 10:48:33,054 - INFO - Result written successfully
2025-04-12 11:42:18,530 - INFO - Starting embedding helper script
2025-04-12 11:42:18,530 - INFO - Reading input from stdin
2025-04-12 11:42:18,531 - INFO - Input data length: 129
2025-04-12 11:42:18,531 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=81
2025-04-12 11:42:18,531 - INFO - Processing text with length: 81 chars
2025-04-12 11:42:18,532 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 11:42:18,532 - INFO - Text length: 81
2025-04-12 11:42:34,493 - INFO - Torch threads set to 1
2025-04-12 11:43:06,941 - INFO - Loading model...
2025-04-12 11:43:06,943 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 11:43:13,824 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-04-12 11:43:30,685 - INFO - Model loaded successfully
2025-04-12 11:43:30,686 - INFO - Generating embedding...
2025-04-12 11:43:30,814 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 11:43:30,815 - INFO - Returning successful result
2025-04-12 11:43:30,815 - INFO - Writing result to stdout
2025-04-12 11:43:30,816 - INFO - Result written successfully
2025-04-12 11:50:27,248 - INFO - Starting embedding helper script
2025-04-12 11:50:27,250 - INFO - Reading input from stdin
2025-04-12 11:50:27,251 - INFO - Input data length: 403
2025-04-12 11:50:27,251 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=72
2025-04-12 11:50:27,252 - INFO - Processing text with length: 72 chars
2025-04-12 11:50:27,252 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 11:50:27,252 - INFO - Text length: 72
2025-04-12 11:50:43,490 - INFO - Torch threads set to 1
2025-04-12 11:51:22,384 - INFO - Loading model...
2025-04-12 11:51:22,386 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 11:51:31,765 - INFO - Model loaded successfully
2025-04-12 11:51:31,766 - INFO - Generating embedding...
2025-04-12 11:51:31,817 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 11:51:31,818 - INFO - Returning successful result
2025-04-12 11:51:31,819 - INFO - Writing result to stdout
2025-04-12 11:51:31,819 - INFO - Result written successfully
2025-04-12 12:13:18,803 - INFO - Starting embedding helper script
2025-04-12 12:13:18,804 - INFO - Reading input from stdin
2025-04-12 12:13:18,804 - INFO - Input data length: 79
2025-04-12 12:13:18,805 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=33
2025-04-12 12:13:18,805 - INFO - Processing text with length: 33 chars
2025-04-12 12:13:18,805 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 12:13:18,806 - INFO - Text length: 33
2025-04-12 12:13:36,261 - INFO - Torch threads set to 1
2025-04-12 12:14:09,977 - INFO - Loading model...
2025-04-12 12:14:09,979 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 12:14:18,244 - INFO - Model loaded successfully
2025-04-12 12:14:18,244 - INFO - Generating embedding...
2025-04-12 12:14:18,823 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 12:14:18,824 - INFO - Returning successful result
2025-04-12 12:14:18,824 - INFO - Writing result to stdout
2025-04-12 12:14:18,826 - INFO - Result written successfully
2025-04-12 12:14:26,323 - INFO - Starting embedding helper script
2025-04-12 12:14:26,324 - INFO - Reading input from stdin
2025-04-12 12:14:26,324 - INFO - Input data length: 78
2025-04-12 12:14:26,324 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=32
2025-04-12 12:14:26,325 - INFO - Processing text with length: 32 chars
2025-04-12 12:14:26,325 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 12:14:26,325 - INFO - Text length: 32
2025-04-12 12:14:40,805 - INFO - Torch threads set to 1
2025-04-12 12:15:11,577 - INFO - Loading model...
2025-04-12 12:15:11,587 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 12:15:17,076 - INFO - Model loaded successfully
2025-04-12 12:15:17,077 - INFO - Generating embedding...
2025-04-12 12:15:17,169 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 12:15:17,171 - INFO - Returning successful result
2025-04-12 12:15:17,172 - INFO - Writing result to stdout
2025-04-12 12:15:17,172 - INFO - Result written successfully
2025-04-12 12:15:29,498 - INFO - Starting embedding helper script
2025-04-12 12:15:29,500 - INFO - Reading input from stdin
2025-04-12 12:15:29,501 - INFO - Input data length: 71
2025-04-12 12:15:29,501 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=25
2025-04-12 12:15:29,501 - INFO - Processing text with length: 25 chars
2025-04-12 12:15:29,501 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 12:15:29,502 - INFO - Text length: 25
2025-04-12 12:15:48,814 - INFO - Torch threads set to 1
2025-04-12 12:16:25,710 - INFO - Loading model...
2025-04-12 12:16:25,716 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 12:16:32,389 - INFO - Model loaded successfully
2025-04-12 12:16:32,390 - INFO - Generating embedding...
2025-04-12 12:16:32,685 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 12:16:32,686 - INFO - Returning successful result
2025-04-12 12:16:32,687 - INFO - Writing result to stdout
2025-04-12 12:16:32,688 - INFO - Result written successfully
2025-04-12 12:16:42,873 - INFO - Starting embedding helper script
2025-04-12 12:16:42,874 - INFO - Reading input from stdin
2025-04-12 12:16:42,875 - INFO - Input data length: 77
2025-04-12 12:16:42,875 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=31
2025-04-12 12:16:42,875 - INFO - Processing text with length: 31 chars
2025-04-12 12:16:42,876 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 12:16:42,876 - INFO - Text length: 31
2025-04-12 12:17:01,552 - INFO - Torch threads set to 1
2025-04-12 12:17:32,714 - INFO - Loading model...
2025-04-12 12:17:32,716 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 12:17:45,863 - INFO - Model loaded successfully
2025-04-12 12:17:45,864 - INFO - Generating embedding...
2025-04-12 12:17:45,889 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 12:17:45,890 - INFO - Returning successful result
2025-04-12 12:17:45,890 - INFO - Writing result to stdout
2025-04-12 12:17:45,890 - INFO - Result written successfully
2025-04-12 12:17:53,769 - INFO - Starting embedding helper script
2025-04-12 12:17:53,770 - INFO - Reading input from stdin
2025-04-12 12:17:53,770 - INFO - Input data length: 73
2025-04-12 12:17:53,770 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=27
2025-04-12 12:17:53,771 - INFO - Processing text with length: 27 chars
2025-04-12 12:17:53,771 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-12 12:17:53,771 - INFO - Text length: 27
2025-04-12 12:18:11,669 - INFO - Torch threads set to 1
2025-04-12 12:18:40,942 - INFO - Loading model...
2025-04-12 12:18:40,943 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-12 12:18:54,021 - INFO - Model loaded successfully
2025-04-12 12:18:54,022 - INFO - Generating embedding...
2025-04-12 12:18:54,046 - INFO - Embedding generated successfully with shape: (384,)
2025-04-12 12:18:54,046 - INFO - Returning successful result
2025-04-12 12:18:54,047 - INFO - Writing result to stdout
2025-04-12 12:18:54,047 - INFO - Result written successfully
2025-04-20 09:25:37,974 - INFO - Starting embedding helper script
2025-04-20 09:25:37,975 - INFO - Reading input from stdin
2025-04-20 09:25:37,976 - INFO - Input data length: 125
2025-04-20 09:25:37,976 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=77
2025-04-20 09:25:37,976 - INFO - Processing text with length: 77 chars
2025-04-20 09:25:37,977 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-20 09:25:37,978 - INFO - Text length: 77
2025-04-20 09:25:42,333 - INFO - Torch threads set to 1
2025-04-20 09:25:48,243 - INFO - Loading model...
2025-04-20 09:25:48,245 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-20 09:25:53,854 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-04-20 09:26:10,309 - INFO - Model loaded successfully
2025-04-20 09:26:10,310 - INFO - Generating embedding...
2025-04-20 09:26:10,420 - INFO - Embedding generated successfully with shape: (384,)
2025-04-20 09:26:10,422 - INFO - Returning successful result
2025-04-20 09:26:10,422 - INFO - Writing result to stdout
2025-04-20 09:26:10,423 - INFO - Result written successfully
2025-04-20 09:28:22,548 - INFO - Starting embedding helper script
2025-04-20 09:28:22,548 - INFO - Reading input from stdin
2025-04-20 09:28:22,549 - INFO - Input data length: 79
2025-04-20 09:28:22,549 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=33
2025-04-20 09:28:22,550 - INFO - Processing text with length: 33 chars
2025-04-20 09:28:22,550 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-20 09:28:22,550 - INFO - Text length: 33
2025-04-20 09:28:26,890 - INFO - Torch threads set to 1
2025-04-20 09:28:32,514 - INFO - Loading model...
2025-04-20 09:28:32,515 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-20 09:28:37,184 - INFO - Model loaded successfully
2025-04-20 09:28:37,184 - INFO - Generating embedding...
2025-04-20 09:28:37,214 - INFO - Embedding generated successfully with shape: (384,)
2025-04-20 09:28:37,215 - INFO - Returning successful result
2025-04-20 09:28:37,215 - INFO - Writing result to stdout
2025-04-20 09:28:37,215 - INFO - Result written successfully
2025-04-20 09:28:38,541 - INFO - Starting embedding helper script
2025-04-20 09:28:38,541 - INFO - Reading input from stdin
2025-04-20 09:28:38,542 - INFO - Input data length: 78
2025-04-20 09:28:38,542 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=32
2025-04-20 09:28:38,543 - INFO - Processing text with length: 32 chars
2025-04-20 09:28:38,543 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-20 09:28:38,543 - INFO - Text length: 32
2025-04-20 09:28:40,932 - INFO - Torch threads set to 1
2025-04-20 09:28:49,044 - INFO - Loading model...
2025-04-20 09:28:49,046 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-20 09:28:53,384 - INFO - Model loaded successfully
2025-04-20 09:28:53,384 - INFO - Generating embedding...
2025-04-20 09:28:53,412 - INFO - Embedding generated successfully with shape: (384,)
2025-04-20 09:28:53,413 - INFO - Returning successful result
2025-04-20 09:28:53,414 - INFO - Writing result to stdout
2025-04-20 09:28:53,414 - INFO - Result written successfully
2025-04-20 09:28:54,894 - INFO - Starting embedding helper script
2025-04-20 09:28:54,894 - INFO - Reading input from stdin
2025-04-20 09:28:54,895 - INFO - Input data length: 71
2025-04-20 09:28:54,895 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=25
2025-04-20 09:28:54,896 - INFO - Processing text with length: 25 chars
2025-04-20 09:28:54,896 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-20 09:28:54,897 - INFO - Text length: 25
2025-04-20 09:28:57,704 - INFO - Torch threads set to 1
2025-04-20 09:29:03,551 - INFO - Loading model...
2025-04-20 09:29:03,552 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-20 09:29:08,339 - INFO - Model loaded successfully
2025-04-20 09:29:08,340 - INFO - Generating embedding...
2025-04-20 09:29:08,366 - INFO - Embedding generated successfully with shape: (384,)
2025-04-20 09:29:08,367 - INFO - Returning successful result
2025-04-20 09:29:08,368 - INFO - Writing result to stdout
2025-04-20 09:29:08,368 - INFO - Result written successfully
2025-04-20 09:29:09,809 - INFO - Starting embedding helper script
2025-04-20 09:29:09,809 - INFO - Reading input from stdin
2025-04-20 09:29:09,810 - INFO - Input data length: 77
2025-04-20 09:29:09,810 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=31
2025-04-20 09:29:09,811 - INFO - Processing text with length: 31 chars
2025-04-20 09:29:09,811 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-20 09:29:09,811 - INFO - Text length: 31
2025-04-20 09:29:12,521 - INFO - Torch threads set to 1
2025-04-20 09:29:18,100 - INFO - Loading model...
2025-04-20 09:29:18,102 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-20 09:29:23,044 - INFO - Model loaded successfully
2025-04-20 09:29:23,044 - INFO - Generating embedding...
2025-04-20 09:29:23,068 - INFO - Embedding generated successfully with shape: (384,)
2025-04-20 09:29:23,069 - INFO - Returning successful result
2025-04-20 09:29:23,070 - INFO - Writing result to stdout
2025-04-20 09:29:23,070 - INFO - Result written successfully
2025-04-20 09:29:25,005 - INFO - Starting embedding helper script
2025-04-20 09:29:25,005 - INFO - Reading input from stdin
2025-04-20 09:29:25,006 - INFO - Input data length: 73
2025-04-20 09:29:25,006 - INFO - Parsed input: model_name=all-MiniLM-L6-v2, text_length=27
2025-04-20 09:29:25,007 - INFO - Processing text with length: 27 chars
2025-04-20 09:29:25,007 - INFO - Starting embedding generation with model: all-MiniLM-L6-v2
2025-04-20 09:29:25,007 - INFO - Text length: 27
2025-04-20 09:29:28,181 - INFO - Torch threads set to 1
2025-04-20 09:29:33,981 - INFO - Loading model...
2025-04-20 09:29:33,983 - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-04-20 09:29:38,755 - INFO - Model loaded successfully
2025-04-20 09:29:38,756 - INFO - Generating embedding...
2025-04-20 09:29:38,780 - INFO - Embedding generated successfully with shape: (384,)
2025-04-20 09:29:38,781 - INFO - Returning successful result
2025-04-20 09:29:38,782 - INFO - Writing result to stdout
2025-04-20 09:29:38,782 - INFO - Result written successfully
