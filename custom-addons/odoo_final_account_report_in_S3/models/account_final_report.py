# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime, date
import json
import logging

_logger = logging.getLogger(__name__)


class AccountFinalReport(models.Model):
    _name = 'account.final.report'
    _description = 'Indian Final Account Report - Schedule 3'
    _order = 'date_from desc, id desc'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    
    name = fields.Char(string='Report Name', required=True, default=lambda self: _('Final Account Report'))
    company_id = fields.Many2one('res.company', string='Company', required=True, 
                                default=lambda self: self.env.company)
    date_from = fields.Date(string='Start Date', required=True,
                           default=lambda self: fields.Date.today().replace(month=4, day=1))
    date_to = fields.Date(string='End Date', required=True,
                         default=lambda self: fields.Date.today())
    include_unposted = fields.Boolean(string='Include Unposted Entries', default=False)
    pl_transfer_mode = fields.Selection([
        ('manual', 'Manual Transfer'),
        ('automatic', 'Automatic Transfer')
    ], string='P&L Transfer Mode', default='manual', required=True)
    
    # Report Data
    balance_sheet_data = fields.Text(string='Balance Sheet Data', readonly=True)
    profit_loss_data = fields.Text(string='Profit & Loss Data', readonly=True)
    report_lines = fields.One2many('account.final.report.line', 'report_id', string='Report Lines')
    
    # Status
    state = fields.Selection([
        ('draft', 'Draft'),
        ('generated', 'Generated'),
        ('exported', 'Exported')
    ], string='Status', default='draft', readonly=True)
    
    # Computed Fields
    total_assets = fields.Monetary(string='Total Assets', compute='_compute_totals', store=True)
    total_liabilities = fields.Monetary(string='Total Liabilities', compute='_compute_totals', store=True)
    total_equity = fields.Monetary(string='Total Equity', compute='_compute_totals', store=True)
    total_income = fields.Monetary(string='Total Income', compute='_compute_totals', store=True)
    total_expenses = fields.Monetary(string='Total Expenses', compute='_compute_totals', store=True)
    net_profit_loss = fields.Monetary(string='Net Profit/Loss', compute='_compute_totals', store=True)
    total_liabilities_equity = fields.Monetary(string='Total Liabilities and Equity', compute='_compute_totals', store=True)
    balance_check = fields.Monetary(string='Balance Check', compute='_compute_totals', store=True,
                                  help='Verification that Assets = Liabilities + Equity (should be zero)')
    currency_id = fields.Many2one('res.currency', related='company_id.currency_id', readonly=True)

    def _get_account_sign_mapping(self):
        """
        Define which account types should reverse signs for reporting.

        For accounts that are typically more debited than credited and that you would
        like to print as negative amounts in your reports, you should reverse the sign
        of the balance; e.g.: Expense account. The same applies for accounts that are
        typically more credited than debited and that you would like to print as positive
        amounts in your reports; e.g.: Income account.

        Returns:
            dict: Mapping of account_type to sign reversal configuration
        """
        return {
            # Balance Sheet Accounts - Show as positive amounts
            'asset_receivable': {'reverse_sign': False, 'display_positive': True},
            'asset_cash': {'reverse_sign': False, 'display_positive': True},
            'asset_current': {'reverse_sign': False, 'display_positive': True},
            'asset_non_current': {'reverse_sign': False, 'display_positive': True},
            'asset_prepayments': {'reverse_sign': False, 'display_positive': True},
            'asset_fixed': {'reverse_sign': False, 'display_positive': True},

            'liability_payable': {'reverse_sign': True, 'display_positive': True},
            'liability_credit_card': {'reverse_sign': True, 'display_positive': True},
            'liability_current': {'reverse_sign': True, 'display_positive': True},
            'liability_non_current': {'reverse_sign': True, 'display_positive': True},

            'equity': {'reverse_sign': True, 'display_positive': True},
            'equity_unaffected': {'reverse_sign': False, 'display_positive': False},  # Keep natural sign

            # P&L Accounts - Handle based on nature
            'income': {'reverse_sign': True, 'display_positive': True},  # Typically credited, show positive
            'income_other': {'reverse_sign': True, 'display_positive': True},

            'expense': {'reverse_sign': False, 'display_positive': True},  # Typically debited, show positive
            'expense_depreciation': {'reverse_sign': False, 'display_positive': True},
            'expense_direct_cost': {'reverse_sign': False, 'display_positive': True},
        }

    def _apply_sign_logic(self, balance, account_type):
        """
        Apply sign logic based on account type for proper financial reporting display.

        Args:
            balance (float): Raw balance from account calculations
            account_type (str): Account type code

        Returns:
            float: Balance with proper sign for reporting
        """
        if not balance:
            return 0.0

        sign_config = self._get_account_sign_mapping().get(account_type, {})
        reverse_sign = sign_config.get('reverse_sign', False)
        display_positive = sign_config.get('display_positive', False)

        # Apply sign reversal if needed
        if reverse_sign:
            balance = -balance

        # Force positive display if required (for Balance Sheet items)
        if display_positive and balance < 0:
            balance = abs(balance)

        return balance

    @api.depends('report_lines.balance')
    def _compute_totals(self):
        for report in self:
            lines = report.report_lines

            # Assets: Always show as positive amounts (already sign-corrected in report lines)
            asset_lines = lines.filtered(lambda l: l.account_type.startswith('asset'))
            report.total_assets = sum(asset_lines.mapped('balance'))

            # Liabilities: Always show as positive amounts (already sign-corrected in report lines)
            liability_lines = lines.filtered(lambda l: l.account_type.startswith('liability'))
            report.total_liabilities = sum(liability_lines.mapped('balance'))

            # Equity: Handle based on account nature (already sign-corrected in report lines)
            equity_lines = lines.filtered(lambda l: l.account_type.startswith('equity'))
            report.total_equity = sum(equity_lines.mapped('balance'))

            # Income: Show as positive for P&L calculation (already sign-corrected in report lines)
            income_lines = lines.filtered(lambda l: l.account_type.startswith('income'))
            report.total_income = sum(income_lines.mapped('balance'))

            # Expenses: Show as positive for P&L calculation (already sign-corrected in report lines)
            expense_lines = lines.filtered(lambda l: l.account_type.startswith('expense'))
            report.total_expenses = sum(expense_lines.mapped('balance'))

            # Net P&L: Income - Expenses
            report.net_profit_loss = report.total_income - report.total_expenses

            # Total Liabilities and Equity
            report.total_liabilities_equity = report.total_liabilities + report.total_equity

            # Enhanced Balance Check: Apply proper sign logic for Balance Sheet equation
            # Assets = Liabilities + Equity (should be zero when balanced)
            # Since all values are already sign-corrected for display, we can directly compare
            report.balance_check = report.total_assets - report.total_liabilities_equity

            # Additional validation: Ensure the balance check is within acceptable tolerance
            # Log detailed information if balance is significantly off
            if abs(report.balance_check) > 0.01:
                _logger.warning(
                    'Balance Sheet equation imbalance detected for report %s: '
                    'Assets=%s, Liabilities=%s, Equity=%s, Difference=%s',
                    report.name, report.total_assets, report.total_liabilities,
                    report.total_equity, report.balance_check
                )

    def _validate_balance_sheet_equation(self):
        """
        Enhanced validation of Balance Sheet equation with proper sign handling.

        This method ensures that Assets = Liabilities + Equity by validating
        that all account types are properly sign-corrected according to
        accounting conventions.

        Returns:
            dict: Validation results with detailed breakdown
        """
        self.ensure_one()

        validation_result = {
            'balanced': False,
            'difference': 0.0,
            'tolerance': 0.01,
            'details': {},
            'recommendations': []
        }

        # Get detailed breakdown by account type
        lines = self.report_lines

        # Assets breakdown (should all be positive)
        asset_breakdown = {}
        for line in lines.filtered(lambda l: l.account_type.startswith('asset')):
            asset_breakdown[line.account_type] = {
                'name': line.name,
                'balance': line.balance,
                'sign_correct': line.balance >= 0  # Assets should be positive
            }

        # Liabilities breakdown (should all be positive for display)
        liability_breakdown = {}
        for line in lines.filtered(lambda l: l.account_type.startswith('liability')):
            liability_breakdown[line.account_type] = {
                'name': line.name,
                'balance': line.balance,
                'sign_correct': line.balance >= 0  # Liabilities should show positive
            }

        # Equity breakdown (handle based on type)
        equity_breakdown = {}
        for line in lines.filtered(lambda l: l.account_type.startswith('equity')):
            expected_positive = line.account_type != 'equity_unaffected'  # Current Year Earnings can be negative
            equity_breakdown[line.account_type] = {
                'name': line.name,
                'balance': line.balance,
                'sign_correct': line.balance >= 0 if expected_positive else True
            }

        # Calculate totals with sign validation
        total_assets = sum([item['balance'] for item in asset_breakdown.values()])
        total_liabilities = sum([item['balance'] for item in liability_breakdown.values()])
        total_equity = sum([item['balance'] for item in equity_breakdown.values()])

        # Balance Sheet equation check
        left_side = total_assets  # Assets
        right_side = total_liabilities + total_equity  # Liabilities + Equity
        difference = left_side - right_side

        validation_result.update({
            'balanced': abs(difference) <= validation_result['tolerance'],
            'difference': difference,
            'details': {
                'assets': {
                    'total': total_assets,
                    'breakdown': asset_breakdown
                },
                'liabilities': {
                    'total': total_liabilities,
                    'breakdown': liability_breakdown
                },
                'equity': {
                    'total': total_equity,
                    'breakdown': equity_breakdown
                },
                'equation': {
                    'left_side': left_side,
                    'right_side': right_side,
                    'difference': difference
                }
            }
        })

        # Generate recommendations if not balanced
        if not validation_result['balanced']:
            if difference > 0:
                validation_result['recommendations'].append(
                    f"Assets exceed Liabilities + Equity by {self.currency_id.format(abs(difference))}. "
                    "Check if some liability or equity accounts have incorrect signs."
                )
            else:
                validation_result['recommendations'].append(
                    f"Liabilities + Equity exceed Assets by {self.currency_id.format(abs(difference))}. "
                    "Check if some asset accounts have incorrect signs."
                )

        # Check for sign issues
        sign_issues = []
        for details in asset_breakdown.values():
            if not details['sign_correct']:
                sign_issues.append(f"Asset account '{details['name']}' has negative balance: {details['balance']}")

        for details in liability_breakdown.values():
            if not details['sign_correct']:
                sign_issues.append(f"Liability account '{details['name']}' has negative balance: {details['balance']}")

        if sign_issues:
            validation_result['recommendations'].extend(sign_issues)

        return validation_result

    def action_detailed_balance_check(self):
        """
        Action to show detailed balance check results in a user-friendly format.
        This can be called from the UI to get comprehensive balance validation.
        """
        self.ensure_one()

        validation_result = self._validate_balance_sheet_equation()

        # Format the results for display
        message_lines = [
            f"Balance Sheet Validation Report for {self.name}",
            "=" * 50,
            "",
            f"Assets Total: {self.currency_id.format(validation_result['details']['assets']['total'])}",
            f"Liabilities Total: {self.currency_id.format(validation_result['details']['liabilities']['total'])}",
            f"Equity Total: {self.currency_id.format(validation_result['details']['equity']['total'])}",
            "",
            f"Balance Check: {self.currency_id.format(validation_result['difference'])}",
            f"Status: {'✅ BALANCED' if validation_result['balanced'] else '❌ IMBALANCED'}",
            ""
        ]

        if validation_result['recommendations']:
            message_lines.extend([
                "Recommendations:",
                "-" * 20
            ])
            for rec in validation_result['recommendations']:
                message_lines.append(f"• {rec}")
            message_lines.append("")

        # Add detailed breakdown
        message_lines.extend([
            "Detailed Breakdown:",
            "-" * 20,
            "",
            "Assets:"
        ])
        for details in validation_result['details']['assets']['breakdown'].values():
            status = "✅" if details['sign_correct'] else "❌"
            message_lines.append(f"  {status} {details['name']}: {self.currency_id.format(details['balance'])}")

        message_lines.extend(["", "Liabilities:"])
        for details in validation_result['details']['liabilities']['breakdown'].values():
            status = "✅" if details['sign_correct'] else "❌"
            message_lines.append(f"  {status} {details['name']}: {self.currency_id.format(details['balance'])}")

        message_lines.extend(["", "Equity:"])
        for details in validation_result['details']['equity']['breakdown'].values():
            status = "✅" if details['sign_correct'] else "❌"
            message_lines.append(f"  {status} {details['name']}: {self.currency_id.format(details['balance'])}")

        # Post detailed message to chatter
        detailed_message = "\n".join(message_lines)
        self.message_post(body=f"<pre>{detailed_message}</pre>")

        # Return notification with summary
        notification_type = 'success' if validation_result['balanced'] else 'danger'
        summary_message = (
            f"Balance Sheet is {'BALANCED' if validation_result['balanced'] else 'IMBALANCED'}. "
            f"Difference: {self.currency_id.format(validation_result['difference'])}"
        )

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': summary_message,
                'type': notification_type,
                'sticky': not validation_result['balanced']
            }
        }

    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for report in self:
            if report.date_from > report.date_to:
                raise UserError(_('Start Date cannot be later than End Date.'))

    def action_generate_report(self):
        """Generate the final account report data"""
        self.ensure_one()
        
        # Clear existing lines
        self.report_lines.unlink()
        
        # Generate report lines
        self._generate_report_lines()
        
        # Generate JSON data for dashboard
        self._generate_json_data()
        
        self.state = 'generated'
        
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    def _generate_report_lines(self):
        """Generate report lines with balance calculations"""
        self._generate_balance_sheet_lines()
        self._generate_profit_loss_lines()
        
    def _generate_balance_sheet_lines(self):
        """Generate Balance Sheet lines"""
        bs_types = {
            # Assets
            'asset_receivable': ('Receivable', 10),
            'asset_cash': ('Bank and Cash', 20),
            'asset_current': ('Current Assets', 30),
            'asset_non_current': ('Non-current Assets', 40),
            'asset_prepayments': ('Prepayments', 50),
            'asset_fixed': ('Fixed Assets', 60),
            
            # Liabilities
            'liability_payable': ('Payable', 110),
            'liability_credit_card': ('Credit Card', 120),
            'liability_current': ('Current Liabilities', 130),
            'liability_non_current': ('Non-current Liabilities', 140),
            
            # Equity
            'equity': ('Equity', 210),
            'equity_unaffected': ('Current Year Earnings', 220),
        }
        
        self._create_report_lines(bs_types)
        
    def _generate_profit_loss_lines(self):
        """Generate Profit & Loss lines"""
        pl_types = {
            # Income
            'income': ('Income', 310),
            'income_other': ('Other Income', 320),
            
            # Expenses
            'expense': ('Expenses', 410),
            'expense_depreciation': ('Depreciation', 420),
            'expense_direct_cost': ('Cost of Revenue', 430),
        }
        
        self._create_report_lines(pl_types)
        
    def _create_report_lines(self, account_types_dict):
        """Create report lines for given account types with proper sign handling"""
        for account_type, (display_name, sequence) in account_types_dict.items():
            accounts = self.env['account.account'].search([
                ('account_type', '=', account_type),
                ('company_ids', 'in', self.company_id.ids)
            ])

            if not accounts:
                continue

            # Calculate raw balances (without abs())
            raw_opening_balance = self._calculate_opening_balance(accounts.ids)
            raw_period_balance = self._calculate_period_balance(accounts.ids)
            raw_closing_balance = raw_opening_balance + raw_period_balance

            # Apply proper sign logic for reporting
            opening_balance = self._apply_sign_logic(raw_opening_balance, account_type)
            period_balance = self._apply_sign_logic(raw_period_balance, account_type)
            closing_balance = self._apply_sign_logic(raw_closing_balance, account_type)

            # Create line even with zero balance for complete report structure
            self.env['account.final.report.line'].create({
                'report_id': self.id,
                'sequence': sequence,
                'account_type': account_type,
                'name': display_name,
                'opening_balance': opening_balance,
                'period_balance': period_balance,
                'balance': closing_balance,
            })

    def _calculate_opening_balance(self, account_ids):
        """
        Calculate opening balance before start date using optimized query.
        Returns raw balance without sign manipulation.
        """
        if not account_ids:
            return 0.0

        domain = [
            ('account_id', 'in', account_ids),
            ('company_id', '=', self.company_id.id),
            ('date', '<', self.date_from),
        ]

        if not self.include_unposted:
            domain.append(('parent_state', '=', 'posted'))

        # Use read_group for better performance
        result = self.env['account.move.line'].read_group(
            domain=domain,
            fields=['balance:sum'],
            groupby=[]
        )
        return result[0]['balance'] if result else 0.0

    def _calculate_period_balance(self, account_ids):
        """
        Calculate balance for the period using optimized query.
        Returns raw balance without sign manipulation.
        """
        if not account_ids:
            return 0.0

        domain = [
            ('account_id', 'in', account_ids),
            ('company_id', '=', self.company_id.id),
            ('date', '>=', self.date_from),
            ('date', '<=', self.date_to),
        ]

        if not self.include_unposted:
            domain.append(('parent_state', '=', 'posted'))

        # Use read_group for better performance
        result = self.env['account.move.line'].read_group(
            domain=domain,
            fields=['balance:sum'],
            groupby=[]
        )
        return result[0]['balance'] if result else 0.0

    def _calculate_display_balance(self, account_ids, account_type, balance_type='closing'):
        """
        Calculate balance with proper sign handling for display purposes.

        Args:
            account_ids (list): List of account IDs
            account_type (str): Account type for sign logic
            balance_type (str): 'opening', 'period', or 'closing'

        Returns:
            float: Balance with proper sign for reporting
        """
        if balance_type == 'opening':
            raw_balance = self._calculate_opening_balance(account_ids)
        elif balance_type == 'period':
            raw_balance = self._calculate_period_balance(account_ids)
        else:  # closing
            opening = self._calculate_opening_balance(account_ids)
            period = self._calculate_period_balance(account_ids)
            raw_balance = opening + period

        return self._apply_sign_logic(raw_balance, account_type)

    def _get_account_type_mapping(self):
        """Get account type mapping for Schedule 3 format with sections"""
        return {
            'balance_sheet': {
                'assets': {
                    'asset_receivable': 'Receivable',
                    'asset_cash': 'Bank and Cash',
                    'asset_current': 'Current Assets',
                    'asset_non_current': 'Non-current Assets',
                    'asset_prepayments': 'Prepayments',
                    'asset_fixed': 'Fixed Assets',
                },
                'liabilities': {
                    'liability_payable': 'Payable',
                    'liability_credit_card': 'Credit Card',
                    'liability_current': 'Current Liabilities',
                    'liability_non_current': 'Non-current Liabilities',
                },
                'equity': {
                    'equity': 'Equity',
                    'equity_unaffected': 'Current Year Earnings',
                }
            },
            'profit_loss': {
                'income': {
                    'income': 'Income',
                    'income_other': 'Other Income',
                },
                'expenses': {
                    'expense': 'Expenses',
                    'expense_depreciation': 'Depreciation',
                    'expense_direct_cost': 'Cost of Revenue',
                }
            }
        }

    def _generate_json_data(self):
        """Generate JSON data for Balance Sheet and P&L in Schedule 3 format"""
        lines = self.report_lines
        
        # Balance Sheet Data - Schedule 3 Format
        balance_sheet = {
            'company_name': self.company_id.name,
            'period': f"{self.date_from.strftime('%d/%m/%Y')} to {self.date_to.strftime('%d/%m/%Y')}",
            'assets': {
                'current_assets': {
                    'items': self._get_section_data(lines, ['asset_current', 'asset_cash', 'asset_receivable']),
                    'total': sum(lines.filtered(lambda l: l.account_type in ['asset_current', 'asset_cash', 'asset_receivable']).mapped('balance'))
                },
                'non_current_assets': {
                    'items': self._get_section_data(lines, ['asset_non_current', 'asset_fixed', 'asset_prepayments']),
                    'total': sum(lines.filtered(lambda l: l.account_type in ['asset_non_current', 'asset_fixed', 'asset_prepayments']).mapped('balance'))
                },
                'total_assets': self.total_assets
            },
            'liabilities_and_equity': {
                'current_liabilities': {
                    'items': self._get_section_data(lines, ['liability_current', 'liability_payable', 'liability_credit_card']),
                    'total': sum(lines.filtered(lambda l: l.account_type in ['liability_current', 'liability_payable', 'liability_credit_card']).mapped('balance'))
                },
                'non_current_liabilities': {
                    'items': self._get_section_data(lines, ['liability_non_current']),
                    'total': sum(lines.filtered(lambda l: l.account_type in ['liability_non_current']).mapped('balance'))
                },
                'equity': {
                    'items': self._get_section_data(lines, ['equity', 'equity_unaffected']),
                    'total': self.total_equity
                },
                'total_liabilities_equity': self.total_liabilities_equity
            }
        }
        
        # P&L Data - Schedule 3 Format
        profit_loss = {
            'company_name': self.company_id.name,
            'period': f"{self.date_from.strftime('%d/%m/%Y')} to {self.date_to.strftime('%d/%m/%Y')}",
            'revenue': {
                'items': self._get_section_data(lines, ['income', 'income_other']),
                'total': sum(lines.filtered(lambda l: l.account_type in ['income', 'income_other']).mapped('balance'))
            },
            'expenses': {
                'items': self._get_section_data(lines, ['expense', 'expense_depreciation', 'expense_direct_cost']),
                'total': sum(lines.filtered(lambda l: l.account_type in ['expense', 'expense_depreciation', 'expense_direct_cost']).mapped('balance'))
            },
            'net_profit_loss': self.net_profit_loss
        }
        
        self.balance_sheet_data = json.dumps(balance_sheet, indent=2)
        self.profit_loss_data = json.dumps(profit_loss, indent=2)

    def _get_section_data(self, lines, account_types):
        """Get data for a specific section with proper sign formatting"""
        section_lines = lines.filtered(lambda l: l.account_type in account_types).sorted('sequence')
        return [{
            'account_type': line.account_type,
            'name': line.name,
            'opening_balance': float(line.opening_balance),
            'period_balance': float(line.period_balance),
            'closing_balance': float(line.balance),
            'formatted_balance': self.currency_id.format(line.balance) if self.currency_id else str(line.balance)
        } for line in section_lines]

    def action_export_excel(self):
        """Export report to Excel format"""
        self.ensure_one()
        
        if self.state != 'generated':
            raise UserError(_('Please generate the report first.'))
        
        # Use Excel export handler
        excel_export = self.env['final.account.excel.export']
        return excel_export.export_to_excel(self.id)

    def action_transfer_pl(self):
        """Transfer P&L to Balance Sheet with immediate form reload"""
        self.ensure_one()

        if self.state != 'generated':
            raise UserError(_('Please generate the report first.'))

        # Check if there's a significant amount to transfer
        if abs(self.net_profit_loss) < 0.01:
            return self._reload_form_with_message(
                _('No significant P&L amount to transfer. Current Net P&L: %s') %
                self.currency_id.format(self.net_profit_loss),
                'warning'
            )

        if self.pl_transfer_mode == 'automatic':
            return self._transfer_pl_automatic()
        else:
            return self._transfer_pl_manual()
    
    def _transfer_pl_automatic(self):
        """Automatic P&L transfer to retained earnings with proper sign handling"""
        # Note: Negligible amount check is now handled in action_transfer_pl

        # Find or create retained earnings line
        retained_earnings_line = self.report_lines.filtered(
            lambda l: l.account_type == 'equity_unaffected'
        )

        # Calculate the transfer amount (keep natural sign for equity)
        transfer_amount = self.net_profit_loss

        if retained_earnings_line:
            # Update existing retained earnings with P&L (natural sign)
            retained_earnings_line.balance += transfer_amount
            retained_earnings_line.period_balance += transfer_amount
        else:
            # Create new retained earnings line with natural sign
            self.env['account.final.report.line'].create({
                'report_id': self.id,
                'sequence': 220,
                'account_type': 'equity_unaffected',
                'name': 'Current Year Earnings',
                'opening_balance': 0.0,
                'period_balance': transfer_amount,
                'balance': transfer_amount,
            })

        # Regenerate totals and JSON data
        self._compute_totals()
        self._generate_json_data()

        # Show success notification and reload the form
        self.message_post(body=_('P&L transferred automatically to retained earnings. Amount: %s') %
                         self.currency_id.format(transfer_amount))

        # Use client action to show notification and reload
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
            'params': {
                'model': 'account.final.report',
                'res_id': self.id,
            }
        }
    
    def _transfer_pl_manual(self):
        """Manual P&L transfer - show confirmation dialog"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Transfer P&L'),
            'res_model': 'account.final.report',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'dialog_size': 'medium',
                'show_pl_transfer_dialog': True,
                'default_net_profit_loss': self.net_profit_loss,
            }
        }

    def action_confirm_manual_pl_transfer(self):
        """Confirm manual P&L transfer and reload the form"""
        self.ensure_one()

        # Perform the same transfer logic as automatic
        if abs(self.net_profit_loss) < 0.01:  # Skip if negligible amount
            return self._reload_form_with_message(_('No significant P&L amount to transfer.'), 'warning')

        # Find or create retained earnings line
        retained_earnings_line = self.report_lines.filtered(
            lambda l: l.account_type == 'equity_unaffected'
        )

        # Calculate the transfer amount (keep natural sign for equity)
        transfer_amount = self.net_profit_loss

        if retained_earnings_line:
            # Update existing retained earnings with P&L (natural sign)
            retained_earnings_line.balance += transfer_amount
            retained_earnings_line.period_balance += transfer_amount
        else:
            # Create new retained earnings line with natural sign
            self.env['account.final.report.line'].create({
                'report_id': self.id,
                'sequence': 220,
                'account_type': 'equity_unaffected',
                'name': 'Current Year Earnings',
                'opening_balance': 0.0,
                'period_balance': transfer_amount,
                'balance': transfer_amount,
            })

        # Regenerate totals and JSON data
        self._compute_totals()
        self._generate_json_data()

        # Show success notification and reload
        return self._reload_form_with_message(
            _('P&L transferred manually to retained earnings. Amount: %s') %
            self.currency_id.format(transfer_amount),
            'success'
        )

    def _reload_form_with_message(self, message, message_type='info'):
        """Helper method to reload form with notification message"""
        # Post message to chatter for permanent record
        # Message type is used for chatter message formatting
        if message_type == 'warning':
            self.message_post(body=f"⚠️ {message}")
        elif message_type == 'danger':
            self.message_post(body=f"❌ {message}")
        else:
            self.message_post(body=f"✅ {message}")

        # Return reload action
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
            'params': {
                'model': 'account.final.report',
                'res_id': self.id,
            }
        }

    def action_reload_form(self):
        """Simple action to reload the current form view"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_window',
            'name': _('Final Account Report'),
            'res_model': 'account.final.report',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    def validate_report_data(self):
        """Validate report data for accuracy with proper sign handling"""
        self.ensure_one()

        errors = []
        warnings = []

        # Enhanced Balance Sheet equation validation with proper sign handling
        balance_validation = self._validate_balance_sheet_equation()

        if not balance_validation['balanced']:
            balance_difference = abs(balance_validation['difference'])

            # Log detailed breakdown for debugging
            _logger.info('Balance Sheet Validation Details:')
            _logger.info('Total Assets: %s', balance_validation['details']['assets']['total'])
            _logger.info('Total Liabilities: %s', balance_validation['details']['liabilities']['total'])
            _logger.info('Total Equity: %s', balance_validation['details']['equity']['total'])
            _logger.info('Balance Check (should be 0): %s', balance_validation['difference'])
            _logger.info('Calculated Difference: %s', balance_difference)

            # Log individual account balances with sign validation
            for line in self.report_lines.sorted('sequence'):
                sign_config = self._get_account_sign_mapping().get(line.account_type, {})
                _logger.info('%s (%s): %s [Sign Config: %s]',
                           line.name, line.account_type, line.balance, sign_config)

            # Add main error
            errors.append(_('Balance Sheet does not balance. Difference: %s') %
                         self.currency_id.format(balance_difference))

            # Add specific recommendations
            if balance_validation['recommendations']:
                errors.extend(balance_validation['recommendations'])
        
        # Check for missing account types (only warn if accounts exist but not in report)
        existing_types = set(self.report_lines.mapped('account_type'))
        expected_types = set(self._get_all_account_types())
        missing_types = expected_types - existing_types
        
        # Only warn if company actually has accounts of these types
        if missing_types:
            account_counts = self.env['account.account'].search_count([
                ('account_type', 'in', list(missing_types)),
                ('company_ids', 'in', self.company_id.ids)
            ])
            if account_counts > 0:
                warnings.append(_('Missing account types: %s') % ', '.join(missing_types))
        
        # Check date range validity
        if self.date_from > self.date_to:
            errors.append(_('Start date cannot be after end date.'))
        # Write chatter with validation results
        if errors or warnings:
            message = ''
            # Adding Balance Sheet Validation Details
            if balance_difference > 0.01:
                message += _('Balance Sheet Validation:\n')
                message += _('Total Assets: %s\n') % self.currency_id.format(self.total_assets)
                message += _('Total Liabilities: %s\n') % self.currency_id.format(self.total_liabilities)
                message += _('Total Equity: %s\n') % self.currency_id.format(self.total_equity)
                message += _('Calculated Difference: %s\n') % self.currency_id.format(balance_difference)
            if errors:
                message += _('Errors:\n') + '\n'.join(errors) + '\n\n'
            if warnings:
                message += _('Warnings:\n') + '\n'.join(warnings)
            self.message_post(body=message)
    
    def _get_all_account_types(self):
        """Get all expected account types for validation"""
        mapping = self._get_account_type_mapping()
        types = []
        for section in mapping.values():
            for subsection in section.values():
                types.extend(subsection.keys())
        return types

    @api.model
    def create_sample_report(self):
        """Create a sample report with demo data for dashboard testing"""
        # Create sample report
        report = self.env['account.final.report'].create({
            'name': 'Sample Final Account Report - Dashboard Test',
            'company_id': self.env.company.id,
            'date_from': fields.Date.today().replace(month=4, day=1),
            'date_to': fields.Date.today(),
            'state': 'generated',
        })

        # Create sample report lines with proper sign handling
        sample_lines = [
            # Assets (positive amounts)
            {'name': 'Accounts Receivable', 'section': 'assets', 'account_type': 'asset_receivable',
             'opening_balance': 50000, 'period_balance': 15000, 'balance': 65000, 'sequence': 1},
            {'name': 'Bank and Cash', 'section': 'assets', 'account_type': 'asset_cash',
             'opening_balance': 25000, 'period_balance': 5000, 'balance': 30000, 'sequence': 2},
            {'name': 'Current Assets', 'section': 'assets', 'account_type': 'asset_current',
             'opening_balance': 30000, 'period_balance': 8000, 'balance': 38000, 'sequence': 3},
            {'name': 'Fixed Assets', 'section': 'assets', 'account_type': 'asset_fixed',
             'opening_balance': 100000, 'period_balance': 5000, 'balance': 105000, 'sequence': 4},
            {'name': 'Non-current Assets', 'section': 'assets', 'account_type': 'asset_non_current',
             'opening_balance': 20000, 'period_balance': 2000, 'balance': 22000, 'sequence': 5},

            # Liabilities (positive amounts for display)
            {'name': 'Accounts Payable', 'section': 'liabilities', 'account_type': 'liability_payable',
             'opening_balance': 35000, 'period_balance': 8000, 'balance': 43000, 'sequence': 6},
            {'name': 'Current Liabilities', 'section': 'liabilities', 'account_type': 'liability_current',
             'opening_balance': 15000, 'period_balance': 3000, 'balance': 18000, 'sequence': 7},

            # Equity (positive amounts for display)
            {'name': 'Share Capital', 'section': 'equity', 'account_type': 'equity',
             'opening_balance': 150000, 'period_balance': 0, 'balance': 150000, 'sequence': 8},
            {'name': 'Current Year Earnings', 'section': 'equity', 'account_type': 'equity_unaffected',
             'opening_balance': 40000, 'period_balance': 20000, 'balance': 60000, 'sequence': 9},

            # Income (positive amounts for P&L)
            {'name': 'Sales Revenue', 'section': 'income', 'account_type': 'income',
             'opening_balance': 0, 'period_balance': 80000, 'balance': 80000, 'sequence': 10},
            {'name': 'Other Income', 'section': 'income', 'account_type': 'income_other',
             'opening_balance': 0, 'period_balance': 5000, 'balance': 5000, 'sequence': 11},

            # Expenses (positive amounts for P&L)
            {'name': 'Operating Expenses', 'section': 'expenses', 'account_type': 'expense',
             'opening_balance': 0, 'period_balance': 45000, 'balance': 45000, 'sequence': 12},
            {'name': 'Depreciation', 'section': 'expenses', 'account_type': 'expense_depreciation',
             'opening_balance': 0, 'period_balance': 5000, 'balance': 5000, 'sequence': 13},
            {'name': 'Cost of Goods Sold', 'section': 'expenses', 'account_type': 'expense_direct_cost',
             'opening_balance': 0, 'period_balance': 15000, 'balance': 15000, 'sequence': 14},
        ]

        for line_data in sample_lines:
            line_data['report_id'] = report.id
            self.env['account.final.report.line'].create(line_data)

        # Trigger computation of totals
        report._compute_totals()

        # Validate balance and log results
        balance_validation = report._validate_balance_sheet_equation()
        if balance_validation['balanced']:
            _logger.info('Sample report created with balanced Balance Sheet. Assets=%s, Liabilities+Equity=%s',
                        report.total_assets, report.total_liabilities_equity)
        else:
            _logger.warning('Sample report created with imbalanced Balance Sheet. Difference=%s',
                           balance_validation['difference'])

        return report

    def test_sign_handling_validation(self):
        """
        Test method to validate sign handling across all account types.
        This method can be called to verify that the sign logic is working correctly.
        """
        self.ensure_one()

        validation_results = {
            'success': True,
            'errors': [],
            'warnings': [],
            'details': {}
        }

        # Test each account type's sign handling
        sign_mapping = self._get_account_sign_mapping()

        for line in self.report_lines:
            account_type = line.account_type
            balance = line.balance

            # Get expected sign behavior
            sign_config = sign_mapping.get(account_type, {})
            display_positive = sign_config.get('display_positive', False)

            # Validate sign logic
            if display_positive and balance < 0:
                validation_results['warnings'].append(
                    f"Account type '{account_type}' ({line.name}) has negative balance {balance} "
                    f"but is configured to display positive"
                )

            # Store details for analysis
            validation_results['details'][line.name] = {
                'account_type': account_type,
                'balance': balance,
                'sign_config': sign_config,
                'expected_positive': display_positive,
                'is_positive': balance >= 0
            }

        # Validate Balance Sheet equation
        balance_difference = abs(self.balance_check)
        if balance_difference > 0.01:
            validation_results['success'] = False
            validation_results['errors'].append(
                f"Balance Sheet equation failed. Assets ({self.total_assets}) != "
                f"Liabilities ({self.total_liabilities}) + Equity ({self.total_equity}). "
                f"Difference: {balance_difference}"
            )

        # Validate P&L calculation
        calculated_pl = self.total_income - self.total_expenses
        if abs(calculated_pl - self.net_profit_loss) > 0.01:
            validation_results['errors'].append(
                f"P&L calculation mismatch. Calculated: {calculated_pl}, "
                f"Stored: {self.net_profit_loss}"
            )

        # Log validation results
        if validation_results['errors']:
            _logger.error("Sign handling validation failed: %s", validation_results['errors'])
        if validation_results['warnings']:
            _logger.warning("Sign handling validation warnings: %s", validation_results['warnings'])

        return validation_results

    def test_pl_transfer_functionality(self):
        """
        Test method to verify P&L transfer functionality works correctly.
        This method can be called to test the transfer process.
        """
        self.ensure_one()

        test_results = {
            'success': True,
            'errors': [],
            'details': {}
        }

        # Store original values
        original_net_pl = self.net_profit_loss
        original_equity_lines = self.report_lines.filtered(lambda l: l.account_type == 'equity_unaffected')
        original_equity_balance = sum(original_equity_lines.mapped('balance'))

        test_results['details']['original_net_pl'] = original_net_pl
        test_results['details']['original_equity_balance'] = original_equity_balance

        # Test automatic transfer
        if self.pl_transfer_mode == 'automatic':
            try:
                # Perform transfer
                self._transfer_pl_automatic()

                # Verify transfer
                new_equity_lines = self.report_lines.filtered(lambda l: l.account_type == 'equity_unaffected')
                new_equity_balance = sum(new_equity_lines.mapped('balance'))

                expected_balance = original_equity_balance + original_net_pl
                if abs(new_equity_balance - expected_balance) > 0.01:
                    test_results['success'] = False
                    test_results['errors'].append(
                        f"Transfer failed. Expected equity balance: {expected_balance}, "
                        f"Actual: {new_equity_balance}"
                    )

                test_results['details']['new_equity_balance'] = new_equity_balance
                test_results['details']['transfer_amount'] = original_net_pl

            except Exception as e:
                test_results['success'] = False
                test_results['errors'].append(f"Transfer failed with error: {str(e)}")

        return test_results