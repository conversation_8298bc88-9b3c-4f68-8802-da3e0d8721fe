# Core dependencies for Odoo 18
# Installed via apt packages:
# python3-psycopg2
# python3-requests
# python3-werkzeug
# python3-gevent
# python3-lxml
# python3-pillow
# python3-jinja2
# python3-yaml
# python3-passlib
# python3-dateutil
# python3-decorator
# python3-docutils
# python3-html2text
# python3-markupsafe
# python3-reportlab
# python3-psutil
# python3-babel
# python3-openssl
# python3-xlrd
# python3-xlwt

# Installed via pip with --break-system-packages:
# pytz (essential for Odoo timezone handling)

# Packages that were not available in apt repositories:
# python3-pytz
# python3-num2words
# python3-pdfminer
# python3-polib
# python3-ldap

# This file is kept as a reference for what packages are needed
# If you need additional packages, try to install them via apt first
# If not available, use pip with --break-system-packages only for essential packages
