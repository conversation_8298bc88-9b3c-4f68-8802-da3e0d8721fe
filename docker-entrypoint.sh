#!/bin/bash

set -e

# Set the postgres database host, port, user and password according to the environment
: ${HOST:=${DB_HOST:='database18-stack'}}
: ${PORT:=${DB_PORT:=5432}}
: ${USER:=${DB_USER:=${POSTGRES_USER:='odoo'}}}
: ${PASSWORD:=${DB_PASSWORD:=${POSTGRES_PASSWORD:='odoo'}}}
: ${DB_NAME:='postgres'}

# Verify Python environment
echo "Using Python: $(which python3)"
echo "Python version: $(python3 --version)"

# Function to check if a parameter exists in Odoo config
DB_ARGS=()
function check_config() {
    param="$1"
    value="$2"
    if ! grep -q -E "^\s*\b${param}\b\s*=" "$ODOO_RC" ; then
        DB_ARGS+=("--${param}")
        DB_ARGS+=("${value}")
    fi
}

# Check if the first argument is a flag
if [ "${1:0:1}" = '-' ]; then
    set -- odoo "$@"
fi

# Wait for PostgreSQL with timeout
echo "Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if PGPASSWORD=$PASSWORD psql -h "$HOST" -U "$USER" -d postgres -c '\q' 2>/dev/null; then
        echo "PostgreSQL is ready!"
        break
    fi
    echo "Waiting for PostgreSQL... ($i/30)"
    sleep 2
    if [ $i -eq 30 ]; then
        echo "Error: PostgreSQL did not become ready in time."
        exit 1
    fi
done

# If odoo command is used
if [ "$1" = 'odoo' ]; then
    # Add database connection parameters if not in config
    check_config "db_host" "$HOST"
    check_config "db_port" "$PORT"
    check_config "db_user" "$USER"
    check_config "db_password" "$PASSWORD"

    # Add additional parameters for better performance
    DB_ARGS+=("--max-cron-threads=1")
    DB_ARGS+=("--workers=0")
    DB_ARGS+=("--limit-memory-hard=805306368")
    DB_ARGS+=("--limit-memory-soft=671088640")
    DB_ARGS+=("--db-template=template0")

    # Ensure proper database initialization
    if [ "$DB_NAME" != "postgres" ]; then
        # Check if database exists
        if ! PGPASSWORD=$PASSWORD psql -h "$HOST" -U "$USER" -d postgres -c "SELECT 1 FROM pg_database WHERE datname = '$DB_NAME'" | grep -q 1; then
            echo "Database '$DB_NAME' does not exist. Creating it..."
            # Create database with template0 to avoid encoding issues
            PGPASSWORD=$PASSWORD createdb -h "$HOST" -U "$USER" -T template0 -E UTF8 "$DB_NAME"

            # Wait a moment to ensure database is fully created
            sleep 2

            # Initialize with base module
            DB_ARGS+=("--database")
            DB_ARGS+=("$DB_NAME")
            DB_ARGS+=("--init")
            DB_ARGS+=("base")
            DB_ARGS+=("--stop-after-init")

            # Run Odoo once to initialize the database
            echo "Initializing database with base module..."
            /usr/bin/odoo "${DB_ARGS[@]}"

            # Reset DB_ARGS for normal startup
            DB_ARGS=()
            check_config "db_host" "$HOST"
            check_config "db_port" "$PORT"
            check_config "db_user" "$USER"
            check_config "db_password" "$PASSWORD"
        else
            echo "Database '$DB_NAME' already exists."
        fi
    fi
fi

echo "Starting Odoo with arguments: $@ ${DB_ARGS[@]}"
exec "$@" "${DB_ARGS[@]}"
