# Use the official Odoo 18 image as the base
FROM odoo:18.0

# Set user to root for package installations
USER root

# Install only essential system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    locales \
    python3-venv \
    python3-dev \
    build-essential \
    && sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen \
    && dpkg-reconfigure --frontend=noninteractive locales \
    && update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

ENV LANG=en_US.UTF-8 \
    LC_ALL=en_US.UTF-8 \
    LANGUAGE=en_US.UTF-8

# Create directories with correct permissions
RUN mkdir -p /var/log/odoo \
    && chown -R odoo:odoo /var/log/odoo

# Install Python packages using apt for what's available
RUN apt-get update && apt-get install -y --no-install-recommends \
    python3-psycopg2 \
    python3-requests \
    python3-werkzeug \
    python3-gevent \
    python3-lxml \
    python3-pillow \
    python3-jinja2 \
    python3-yaml \
    python3-passlib \
    python3-dateutil \
    python3-decorator \
    python3-docutils \
    python3-html2text \
    python3-markupsafe \
    python3-reportlab \
    python3-psutil \
    python3-babel \
    python3-openssl \
    python3-xlrd \
    python3-xlwt \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# For a few essential packages not available in apt, use pip with --break-system-packages
# This is not ideal but necessary for Odoo to function properly
RUN pip3 install --break-system-packages pytz

# Note: We're minimizing pip usage to avoid conflicts with Debian packages
# Only using it for essential packages that are not available via apt

# Set environment variables
ENV PYTHONUNBUFFERED=1

# Copy and configure Odoo config file
COPY config/odoo.conf /etc/odoo/
RUN chown odoo:odoo /etc/odoo/odoo.conf && \
    chmod 0640 /etc/odoo/odoo.conf

# Copy entrypoint script and make it executable
COPY docker-entrypoint.sh /
RUN chmod +x /docker-entrypoint.sh && \
    chown odoo:odoo /docker-entrypoint.sh

# Expose ports
EXPOSE 8069 8072

# Switch back to odoo user for security
USER odoo

# Set entrypoint
ENTRYPOINT ["/docker-entrypoint.sh"]
